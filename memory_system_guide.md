# Hướng Dẫn Memory System - Enhanced Keno Predictor

## Tổng Quan

Memory System là tính năng thông minh mới được thêm vào Enhanced Keno Predictor để:

1. **Ghi nhớ số hay ra quá**: Phân tích số nào được dự đoán sai nhiều lần
2. **Cooldown thông minh**: Tạm thời không dự đoán số hay ra quá trong N kì
3. **Học từ lịch sử**: C<PERSON>i thiện độ chính xác dự đoán theo thời gian
4. **Tránh lặp lại sai lầm**: Không dự đoán lại số đã sai nhiều lần

## Cách Hoạt Động

### 1. Phân Tích Lịch Sử
```
📊 Phân tích 50 kì gần nhất
🔍 Tìm số có tần suất > 70% (hay ra quá)
📈 T<PERSON><PERSON> mức độ "over-frequent" cho từng số
```

### 2. Tracking Dự Đoán
```
🎯 Mỗi lần dự đoán → <PERSON><PERSON> nhận vào memory
✅ N<PERSON>u đúng (s<PERSON> trượt) → Tăng correct_count
❌ Nếu sai (số ra) → Tăng wrong_count + streak
```

### 3. Cooldown System
```
🚫 Nếu dự đoán sai ≥ 3 lần liên tiếp
⏰ Đưa số đó vào cooldown 10 kì
🔄 Không dự đoán số này trong thời gian cooldown
```

## Cấu Hình Mặc Định

```python
memory_config = {
    'over_prediction_threshold': 3,    # Sai 3 lần → cooldown
    'cooldown_periods': 10,            # Cooldown 10 kì (80 phút)
    'analysis_window': 50,             # Phân tích 50 kì gần nhất
    'frequency_threshold': 0.7         # Ngưỡng hay ra quá 70%
}
```

## Cách Sử Dụng

### 1. Dự Đoán Với Memory
```python
from enhanced_keno_predictor import EnhancedKenoPredictor

predictor = EnhancedKenoPredictor()

# Dự đoán tự động áp dụng memory
result = predictor.predict_from_database()

# Kết quả đã được lọc qua memory system
ensemble_prediction = result['ensemble']
```

### 2. Cập Nhật Memory Với Kết Quả Thực Tế
```python
# Sau khi có kết quả thực tế
predicted_numbers = [1, 5, 12, 23, 45, 67]
actual_results = [10, 20, 30, 40, 50, 60]  # Số ra thực tế

# Cập nhật memory
predictor.update_prediction_memory(predicted_numbers, actual_results)
```

### 3. Kiểm Tra Blacklist
```python
# Xem số nào đang trong cooldown
blacklisted = predictor.get_blacklisted_numbers()

for item in blacklisted:
    print(f"Số {item['number']}: còn {item['remaining_periods']} kì")
    print(f"Lý do: {item['reason']}")
```

### 4. Báo Cáo Memory
```python
# Xem báo cáo chi tiết
predictor.get_memory_report()

# Thống kê accuracy
stats = predictor.get_memory_statistics()
print(f"Tracking {stats['total_numbers_tracked']} số")
print(f"Cooldown {stats['numbers_in_cooldown']} số")
```

## Ví Dụ Thực Tế

### Scenario: Số 15 hay ra quá

```
Kì 1: Dự đoán [15, 23, 45, 67, 12, 8] → Kết quả [15, 20, 30, 40, 50, 60]
      → Số 15 ra (sai), streak = 1

Kì 2: Dự đoán [15, 28, 44, 66, 11, 9] → Kết quả [15, 25, 35, 45, 55, 65] 
      → Số 15 ra (sai), streak = 2

Kì 3: Dự đoán [15, 29, 43, 65, 10, 7] → Kết quả [15, 26, 36, 46, 56, 66]
      → Số 15 ra (sai), streak = 3
      → 🚫 Số 15 vào cooldown 10 kì

Kì 4-13: Số 15 không được dự đoán (trong cooldown)

Kì 14: Số 15 có thể được dự đoán lại
```

## Lợi Ích

### ✅ Cải Thiện Độ Chính Xác
- Tránh dự đoán số hay ra quá
- Học từ sai lầm trong quá khứ
- Tự động điều chỉnh theo pattern thực tế

### ✅ Thông Minh Hơn
- Ghi nhớ lịch sử dự đoán
- Phân tích xu hướng dài hạn
- Tự động blacklist số "khó dự đoán"

### ✅ Linh Hoạt
- Cấu hình được các tham số
- Lưu/load memory từ file
- Test với dữ liệu mẫu

## Cấu Hình Tùy Chỉnh

### Nghiêm Ngặt Hơn
```python
predictor.configure_memory_system(
    over_prediction_threshold=2,  # Sai 2 lần → cooldown
    cooldown_periods=15,          # Cooldown 15 kì
    frequency_threshold=0.6       # Ngưỡng 60%
)
```

### Lỏng Lẻo Hơn
```python
predictor.configure_memory_system(
    over_prediction_threshold=5,  # Sai 5 lần → cooldown
    cooldown_periods=5,           # Cooldown 5 kì
    frequency_threshold=0.8       # Ngưỡng 80%
)
```

## Quản Lý Memory

### Lưu Memory
```python
# Lưu memory để sử dụng lần sau
predictor.save_memory_to_file("my_memory.json")
```

### Load Memory
```python
# Load memory đã lưu
predictor.load_memory_from_file("my_memory.json")
```

### Xóa Memory
```python
# Reset memory system
predictor.clear_memory()
```

## Tích Hợp Với Workflow

### Workflow Hàng Ngày
```python
# 1. Load memory từ file
predictor.load_memory_from_file()

# 2. Dự đoán với memory
result = predictor.predict_from_database()
predicted = result['ensemble']

# 3. Sau khi có kết quả thực tế
actual = get_actual_results()  # Lấy kết quả thực tế
predictor.update_prediction_memory(predicted, actual)

# 4. Lưu memory
predictor.save_memory_to_file()
```

### Monitoring
```python
# Kiểm tra hiệu quả memory
predictor.get_memory_report()

# Nếu có quá nhiều số trong cooldown
blacklisted = predictor.get_blacklisted_numbers()
if len(blacklisted) > 20:  # Quá 20 số
    # Điều chỉnh cấu hình
    predictor.configure_memory_system(
        over_prediction_threshold=4,  # Tăng threshold
        cooldown_periods=8            # Giảm cooldown
    )
```

## Kết Luận

Memory System là bước tiến quan trọng giúp Enhanced Keno Predictor:

1. **Thông minh hơn**: Học từ lịch sử và tránh lặp lại sai lầm
2. **Chính xác hơn**: Lọc bỏ số hay ra quá khỏi dự đoán
3. **Tự động hóa**: Không cần can thiệp thủ công
4. **Linh hoạt**: Cấu hình được theo nhu cầu

Đây là đặc trưng riêng biệt giúp hệ thống dự đoán ngày càng tốt hơn theo thời gian!
