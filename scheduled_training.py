#!/usr/bin/env python3
"""
Scheduled Training System - Train model theo <PERSON><PERSON><PERSON>ng thời gian tùy chỉnh
Hỗ trợ training đ<PERSON><PERSON> kỳ, incremental training, và full retraining
"""

import os
import time
import schedule
import threading
from datetime import datetime, timedelta
from variable_length_model import connect_db, print_header, print_success, print_info, print_warning, print_error
from train_keno_model import train_full_model, test_model_performance
from incremental_train import IncrementalTrainer
import json

class ScheduledTrainer:
    """
    Hệ thống training theo lịch trình
    """

    def __init__(self):
        self.is_running = False
        self.training_thread = None
        self.config_file = "training_schedule.json"
        self.log_file = "training_log.txt"

        # C<PERSON>u hình mặc định
        self.config = {
            "auto_training": {
                "enabled": False,
                "interval_hours": 24,        # Train mỗi 24 giờ
                "training_type": "incremental",  # "incremental" hoặc "full"
                "min_new_data_days": 1,      # <PERSON><PERSON>n ít nhất 1 ngày data mới
                "max_training_time": 6       # Tối đa 6 giờ training
            },
            "data_requirements": {
                "min_days_for_full": 30,     # <PERSON><PERSON><PERSON> ít nhất 30 ngày cho full training
                "min_records_per_day": 100,  # Ít nhất 100 records/ngày
                "data_quality_threshold": 0.95  # 95% data quality
            },
            "model_backup": {
                "backup_before_training": True,
                "keep_backups": 5,           # Giữ 5 backup gần nhất
                "backup_folder": "model_backups"
            },
            "notifications": {
                "log_to_file": True,
                "print_progress": True,
                "save_training_stats": True
            }
        }

        self.load_config()

    def load_config(self):
        """Load cấu hình từ file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                print_success(f"✅ Đã load cấu hình từ {self.config_file}")
        except Exception as e:
            print_warning(f"⚠️ Không thể load cấu hình: {e}")

    def save_config(self):
        """Lưu cấu hình vào file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            print_success(f"✅ Đã lưu cấu hình vào {self.config_file}")
        except Exception as e:
            print_error(f"❌ Lỗi lưu cấu hình: {e}")

    def log_message(self, message):
        """Ghi log"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"

        if self.config["notifications"]["print_progress"]:
            print(log_entry)

        if self.config["notifications"]["log_to_file"]:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(log_entry + "\n")
            except Exception as e:
                print_warning(f"⚠️ Lỗi ghi log: {e}")

    def check_data_availability(self):
        """Kiểm tra dữ liệu có sẵn"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            # Kiểm tra data mới nhất
            cursor.execute("SELECT MAX(date) as latest_date, COUNT(DISTINCT date) as total_days FROM histories_keno")
            result = cursor.fetchone()

            latest_date = result['latest_date']
            total_days = result['total_days']

            # Kiểm tra data trong N ngày gần nhất
            min_days = self.config["data_requirements"]["min_days_for_full"]
            cursor.execute("""
                SELECT COUNT(DISTINCT date) as recent_days, COUNT(*) as total_records
                FROM histories_keno
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
            """, (min_days,))

            recent_data = cursor.fetchone()
            cursor.close()
            conn.close()

            return {
                'latest_date': latest_date,
                'total_days': total_days,
                'recent_days': recent_data['recent_days'],
                'total_records': recent_data['total_records'],
                'avg_records_per_day': recent_data['total_records'] / max(recent_data['recent_days'], 1)
            }

        except Exception as e:
            self.log_message(f"❌ Lỗi kiểm tra dữ liệu: {e}")
            return None

    def check_new_data_since_last_training(self):
        """Kiểm tra data mới từ lần training cuối"""
        try:
            # Đọc thời gian training cuối từ log
            last_training_date = None
            if os.path.exists("training_history.json"):
                with open("training_history.json", 'r') as f:
                    history = json.load(f)
                    if history and len(history) > 0:
                        last_training_date = history[-1].get('training_date')

            if not last_training_date:
                return True  # Chưa có lịch sử training

            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            cursor.execute("""
                SELECT COUNT(DISTINCT date) as new_days
                FROM histories_keno
                WHERE date > %s
            """, (last_training_date,))

            result = cursor.fetchone()
            cursor.close()
            conn.close()

            new_days = result['new_days']
            min_days = self.config["auto_training"]["min_new_data_days"]

            return new_days >= min_days

        except Exception as e:
            self.log_message(f"⚠️ Lỗi kiểm tra data mới: {e}")
            return True  # Mặc định cho phép training

    def backup_current_model(self):
        """Backup model hiện tại"""
        if not self.config["model_backup"]["backup_before_training"]:
            return True

        try:
            backup_folder = self.config["model_backup"]["backup_folder"]
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Backup model file
            if os.path.exists("keno_variable_length_model.h5"):
                backup_path = f"{backup_folder}/model_backup_{timestamp}.h5"
                os.system(f"cp keno_variable_length_model.h5 {backup_path}")
                self.log_message(f"📦 Đã backup model: {backup_path}")

            # Backup stats
            if os.path.exists("model_stats.pkl"):
                backup_stats = f"{backup_folder}/stats_backup_{timestamp}.pkl"
                os.system(f"cp model_stats.pkl {backup_stats}")

            # Cleanup old backups
            self.cleanup_old_backups()

            return True

        except Exception as e:
            self.log_message(f"❌ Lỗi backup model: {e}")
            return False

    def cleanup_old_backups(self):
        """Xóa backup cũ"""
        try:
            backup_folder = self.config["model_backup"]["backup_folder"]
            keep_count = self.config["model_backup"]["keep_backups"]

            if not os.path.exists(backup_folder):
                return

            # Lấy danh sách backup files
            backup_files = [f for f in os.listdir(backup_folder) if f.startswith("model_backup_")]
            backup_files.sort(reverse=True)  # Mới nhất trước

            # Xóa backup cũ
            for old_backup in backup_files[keep_count:]:
                old_path = os.path.join(backup_folder, old_backup)
                os.remove(old_path)
                self.log_message(f"🗑️ Đã xóa backup cũ: {old_backup}")

        except Exception as e:
            self.log_message(f"⚠️ Lỗi cleanup backup: {e}")

    def perform_training(self, training_type="incremental"):
        """Thực hiện training"""
        self.log_message(f"🚀 Bắt đầu {training_type} training")
        start_time = datetime.now()

        try:
            # Backup model hiện tại
            if not self.backup_current_model():
                self.log_message("⚠️ Backup thất bại, tiếp tục training")

            success = False

            if training_type == "incremental":
                # Incremental training
                trainer = IncrementalTrainer()
                success = trainer.run_incremental_training()

            elif training_type == "full":
                # Full training
                success = train_full_model()

            end_time = datetime.now()
            duration = end_time - start_time

            if success:
                self.log_message(f"✅ {training_type.title()} training thành công!")
                self.log_message(f"⏱️ Thời gian: {duration}")

                # Test model performance
                self.log_message("🧪 Testing model performance...")
                test_model_performance()

                # Lưu training history
                self.save_training_history(training_type, start_time, end_time, True)

            else:
                self.log_message(f"❌ {training_type.title()} training thất bại!")
                self.save_training_history(training_type, start_time, end_time, False)

            return success

        except Exception as e:
            self.log_message(f"❌ Lỗi training: {e}")
            return False

    def save_training_history(self, training_type, start_time, end_time, success):
        """Lưu lịch sử training"""
        try:
            history_entry = {
                "training_type": training_type,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_minutes": (end_time - start_time).total_seconds() / 60,
                "success": success,
                "training_date": start_time.strftime("%Y-%m-%d")
            }

            history = []
            if os.path.exists("training_history.json"):
                with open("training_history.json", 'r') as f:
                    history = json.load(f)

            history.append(history_entry)

            # Giữ chỉ 50 records gần nhất
            history = history[-50:]

            with open("training_history.json", 'w') as f:
                json.dump(history, f, indent=2)

        except Exception as e:
            self.log_message(f"⚠️ Lỗi lưu training history: {e}")

    def should_train(self):
        """Kiểm tra có nên training không"""
        # Kiểm tra data availability
        data_info = self.check_data_availability()
        if not data_info:
            return False, "Không thể kiểm tra dữ liệu"

        # Kiểm tra quality
        min_records = self.config["data_requirements"]["min_records_per_day"]
        if data_info['avg_records_per_day'] < min_records:
            return False, f"Không đủ dữ liệu: {data_info['avg_records_per_day']:.1f} < {min_records} records/day"

        # Kiểm tra data mới
        if not self.check_new_data_since_last_training():
            return False, "Không có dữ liệu mới"

        return True, "OK"

    def scheduled_training_job(self):
        """Job training được schedule"""
        self.log_message("⏰ Scheduled training job triggered")

        # Kiểm tra có nên training không
        should_train, reason = self.should_train()

        if not should_train:
            self.log_message(f"⏭️ Bỏ qua training: {reason}")
            return

        # Thực hiện training
        training_type = self.config["auto_training"]["training_type"]
        success = self.perform_training(training_type)

        if success:
            self.log_message("🎉 Scheduled training hoàn thành thành công!")
        else:
            self.log_message("😞 Scheduled training thất bại!")

    def start_scheduler(self):
        """Bắt đầu scheduler"""
        if self.is_running:
            print_warning("⚠️ Scheduler đã đang chạy")
            return

        if not self.config["auto_training"]["enabled"]:
            print_warning("⚠️ Auto training chưa được bật")
            return

        interval_hours = self.config["auto_training"]["interval_hours"]

        # Clear existing schedules
        schedule.clear()

        # Schedule training job
        schedule.every(interval_hours).hours.do(self.scheduled_training_job)

        self.is_running = True
        self.log_message(f"🚀 Scheduler started - Training mỗi {interval_hours} giờ")

        # Chạy scheduler trong thread riêng
        def run_scheduler():
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Check mỗi phút

        self.training_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.training_thread.start()

        print_success("✅ Scheduler đã bắt đầu!")

    def stop_scheduler(self):
        """Dừng scheduler"""
        self.is_running = False
        schedule.clear()

        if self.training_thread:
            self.training_thread.join(timeout=5)

        self.log_message("🛑 Scheduler đã dừng")
        print_success("✅ Scheduler đã dừng!")

    def train_now(self, training_type="incremental"):
        """Training ngay lập tức"""
        print_header(f"TRAINING NGAY - {training_type.upper()}")

        # Kiểm tra điều kiện
        should_train, reason = self.should_train()
        if not should_train:
            print_warning(f"⚠️ Không thể training: {reason}")
            return False

        # Thực hiện training
        return self.perform_training(training_type)

    def train_with_date_range(self, start_date, end_date, training_type="full"):
        """Training với khoảng thời gian cụ thể"""
        print_header(f"TRAINING VỚI KHOẢNG THỜI GIAN: {start_date} - {end_date}")

        try:
            # Kiểm tra dữ liệu trong khoảng thời gian
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            cursor.execute("""
                SELECT COUNT(*) as total_records, COUNT(DISTINCT date) as total_days
                FROM histories_keno
                WHERE date BETWEEN %s AND %s
            """, (start_date, end_date))

            result = cursor.fetchone()
            cursor.close()
            conn.close()

            if result['total_records'] < 1000:
                print_warning(f"⚠️ Không đủ dữ liệu: chỉ có {result['total_records']} records")
                return False

            print_info(f"📊 Tìm thấy {result['total_records']} records trong {result['total_days']} ngày")

            # Backup và training
            if training_type == "full":
                return self.perform_training("full")
            else:
                return self.perform_training("incremental")

        except Exception as e:
            print_error(f"❌ Lỗi training với date range: {e}")
            return False

    def configure_auto_training(self, enabled=None, interval_hours=None,
                               training_type=None, min_new_data_days=None):
        """Cấu hình auto training"""
        print_header("CẤU HÌNH AUTO TRAINING")

        if enabled is not None:
            self.config["auto_training"]["enabled"] = enabled
        if interval_hours is not None:
            self.config["auto_training"]["interval_hours"] = interval_hours
        if training_type is not None:
            self.config["auto_training"]["training_type"] = training_type
        if min_new_data_days is not None:
            self.config["auto_training"]["min_new_data_days"] = min_new_data_days

        self.save_config()

        print_info("📊 Cấu hình auto training:")
        auto_config = self.config["auto_training"]
        for key, value in auto_config.items():
            print_info(f"   • {key}: {value}")

    def get_training_status(self):
        """Lấy trạng thái training"""
        print_header("TRẠNG THÁI TRAINING")

        # Scheduler status
        print_info(f"🔄 Scheduler: {'Đang chạy' if self.is_running else 'Đã dừng'}")
        print_info(f"⚙️ Auto training: {'Bật' if self.config['auto_training']['enabled'] else 'Tắt'}")

        if self.config["auto_training"]["enabled"]:
            interval = self.config["auto_training"]["interval_hours"]
            training_type = self.config["auto_training"]["training_type"]
            print_info(f"⏰ Interval: {interval} giờ")
            print_info(f"🎯 Type: {training_type}")

        # Data status
        data_info = self.check_data_availability()
        if data_info:
            print_info(f"📅 Data mới nhất: {data_info['latest_date']}")
            print_info(f"📊 Tổng ngày: {data_info['total_days']}")
            print_info(f"📈 Records/ngày: {data_info['avg_records_per_day']:.1f}")

        # Training history
        try:
            if os.path.exists("training_history.json"):
                with open("training_history.json", 'r') as f:
                    history = json.load(f)
                    if history:
                        last_training = history[-1]
                        print_info(f"🕐 Training cuối: {last_training['start_time']}")
                        print_info(f"✅ Thành công: {'Có' if last_training['success'] else 'Không'}")
                        print_info(f"⏱️ Thời gian: {last_training['duration_minutes']:.1f} phút")
        except:
            pass

        # Next scheduled training
        if self.is_running and schedule.jobs:
            next_run = schedule.next_run()
            if next_run:
                print_info(f"⏭️ Training tiếp theo: {next_run}")

    def get_training_history(self, limit=10):
        """Lấy lịch sử training"""
        try:
            if not os.path.exists("training_history.json"):
                print_info("📊 Chưa có lịch sử training")
                return

            with open("training_history.json", 'r') as f:
                history = json.load(f)

            if not history:
                print_info("📊 Chưa có lịch sử training")
                return

            print_header(f"LỊCH SỬ TRAINING ({len(history)} lần)")

            for i, entry in enumerate(history[-limit:], 1):
                status = "✅" if entry['success'] else "❌"
                print_info(f"{i:2d}. {status} {entry['training_type'].upper()} - "
                          f"{entry['start_time'][:16]} - "
                          f"{entry['duration_minutes']:.1f}p")

        except Exception as e:
            print_error(f"❌ Lỗi đọc lịch sử: {e}")

def interactive_training_manager():
    """Giao diện tương tác cho training manager"""
    print_header("SCHEDULED TRAINING MANAGER")

    trainer = ScheduledTrainer()

    while True:
        print_info("\n📋 MENU TRAINING MANAGER:")
        print("1. 🚀 Training ngay (Incremental)")
        print("2. 🔄 Training ngay (Full)")
        print("3. 📅 Training với khoảng thời gian")
        print("4. ⚙️ Cấu hình auto training")
        print("5. 🔄 Bắt đầu scheduler")
        print("6. 🛑 Dừng scheduler")
        print("7. 📊 Trạng thái training")
        print("8. 📜 Lịch sử training")
        print("9. 🧪 Test model hiện tại")
        print("10. Thoát")

        try:
            choice = input("\nNhập lựa chọn (1-10): ").strip()

            if choice == '1':
                trainer.train_now("incremental")

            elif choice == '2':
                confirm = input("⚠️ Full training có thể mất nhiều giờ. Tiếp tục? (y/N): ").strip().lower()
                if confirm == 'y':
                    trainer.train_now("full")

            elif choice == '3':
                start_date = input("Ngày bắt đầu (YYYY-MM-DD): ").strip()
                end_date = input("Ngày kết thúc (YYYY-MM-DD): ").strip()
                training_type = input("Loại training (full/incremental) [full]: ").strip() or "full"

                if start_date and end_date:
                    trainer.train_with_date_range(start_date, end_date, training_type)
                else:
                    print_warning("⚠️ Cần nhập đầy đủ ngày bắt đầu và kết thúc")

            elif choice == '4':
                print_info("Cấu hình Auto Training (Enter để giữ nguyên):")

                current = trainer.config["auto_training"]

                enabled_input = input(f"Bật auto training? (y/n) [{current['enabled']}]: ").strip().lower()
                enabled = current['enabled']
                if enabled_input == 'y':
                    enabled = True
                elif enabled_input == 'n':
                    enabled = False

                interval_input = input(f"Interval (giờ) [{current['interval_hours']}]: ").strip()
                interval_hours = int(interval_input) if interval_input else current['interval_hours']

                type_input = input(f"Training type (incremental/full) [{current['training_type']}]: ").strip()
                training_type = type_input if type_input else current['training_type']

                min_days_input = input(f"Min new data days [{current['min_new_data_days']}]: ").strip()
                min_new_data_days = int(min_days_input) if min_days_input else current['min_new_data_days']

                trainer.configure_auto_training(enabled, interval_hours, training_type, min_new_data_days)

            elif choice == '5':
                trainer.start_scheduler()

            elif choice == '6':
                trainer.stop_scheduler()

            elif choice == '7':
                trainer.get_training_status()

            elif choice == '8':
                limit_input = input("Số lượng records hiển thị [10]: ").strip()
                limit = int(limit_input) if limit_input else 10
                trainer.get_training_history(limit)

            elif choice == '9':
                print_info("🧪 Testing model hiện tại...")
                test_model_performance()

            elif choice == '10':
                if trainer.is_running:
                    confirm = input("Scheduler đang chạy. Dừng trước khi thoát? (y/N): ").strip().lower()
                    if confirm == 'y':
                        trainer.stop_scheduler()
                print_info("Thoát Training Manager")
                break

            else:
                print_warning("Lựa chọn không hợp lệ")

        except KeyboardInterrupt:
            print_info("\nThoát Training Manager")
            if trainer.is_running:
                trainer.stop_scheduler()
            break
        except Exception as e:
            print_error(f"❌ Lỗi: {e}")

def quick_training_commands():
    """Các lệnh training nhanh"""
    import sys

    if len(sys.argv) < 2:
        print_info("Sử dụng:")
        print_info("  python scheduled_training.py train_now [incremental|full]")
        print_info("  python scheduled_training.py start_scheduler")
        print_info("  python scheduled_training.py stop_scheduler")
        print_info("  python scheduled_training.py status")
        print_info("  python scheduled_training.py history")
        print_info("  python scheduled_training.py interactive")
        return

    command = sys.argv[1]
    trainer = ScheduledTrainer()

    if command == "train_now":
        training_type = sys.argv[2] if len(sys.argv) > 2 else "incremental"
        trainer.train_now(training_type)

    elif command == "start_scheduler":
        trainer.start_scheduler()
        print_info("Scheduler đã bắt đầu. Nhấn Ctrl+C để dừng.")
        try:
            while trainer.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            trainer.stop_scheduler()

    elif command == "stop_scheduler":
        trainer.stop_scheduler()

    elif command == "status":
        trainer.get_training_status()

    elif command == "history":
        limit = int(sys.argv[2]) if len(sys.argv) > 2 else 10
        trainer.get_training_history(limit)

    elif command == "interactive":
        interactive_training_manager()

    else:
        print_warning(f"Lệnh không hợp lệ: {command}")

def demo_scheduled_training():
    """Demo scheduled training"""
    print_header("DEMO SCHEDULED TRAINING")

    trainer = ScheduledTrainer()

    # Hiển thị cấu hình hiện tại
    print_info("📊 Cấu hình hiện tại:")
    trainer.get_training_status()

    # Demo cấu hình
    print_info("\n⚙️ Demo cấu hình auto training...")
    trainer.configure_auto_training(
        enabled=True,
        interval_hours=12,  # Mỗi 12 giờ
        training_type="incremental",
        min_new_data_days=1
    )

    # Demo kiểm tra điều kiện
    print_info("\n🔍 Kiểm tra điều kiện training...")
    should_train, reason = trainer.should_train()
    print_info(f"Có thể training: {'Có' if should_train else 'Không'} - {reason}")

    # Demo training history
    print_info("\n📜 Lịch sử training:")
    trainer.get_training_history(5)

    print_success("\n✅ Demo hoàn thành!")
    print_info("💡 Sử dụng 'python scheduled_training.py interactive' để trải nghiệm đầy đủ")

if __name__ == "__main__":
    import sys

    # Cài đặt schedule package nếu chưa có
    try:
        import schedule
    except ImportError:
        print_error("❌ Cần cài đặt package 'schedule'")
        print_info("Chạy: pip install schedule")
        sys.exit(1)

    if len(sys.argv) > 1:
        quick_training_commands()
    else:
        interactive_training_manager()
