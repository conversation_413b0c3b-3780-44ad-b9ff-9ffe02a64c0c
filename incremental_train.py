#!/usr/bin/env python3
"""
Incremental Training cho Variable Length Keno Model
Chỉ train data mới, không train lại từ đầu
"""

import tensorflow as tf
import pandas as pd
import pickle
import os
from datetime import datetime, timedelta
from variable_length_model import VariableLengthKenoModel, connect_db, print_header, print_success, print_info, print_warning

class IncrementalTrainer:
    """
    Quản lý incremental training cho Keno model
    """
    
    def __init__(self, model_path='keno_variable_length_model.h5', 
                 stats_path='model_stats.pkl',
                 training_log_path='training_log.pkl'):
        self.model_path = model_path
        self.stats_path = stats_path
        self.training_log_path = training_log_path
        self.model = None
        self.stats = None
        self.training_log = None
        
    def load_existing_model(self):
        """Load model và stats hiện có"""
        print_header("LOAD MODEL HIỆN CÓ")
        
        try:
            # Load model
            if os.path.exists(self.model_path):
                self.model = VariableLengthKenoModel()
                self.model.model = tf.keras.models.load_model(self.model_path)
                print_success(f"✅ Đã load model từ {self.model_path}")
            else:
                print_warning("❌ Không tìm thấy model hiện có")
                return False
            
            # Load stats
            if os.path.exists(self.stats_path):
                with open(self.stats_path, 'rb') as f:
                    self.stats = pickle.load(f)
                print_success("✅ Đã load stats")
            else:
                print_warning("⚠️ Không tìm thấy stats")
                
            # Load training log
            if os.path.exists(self.training_log_path):
                with open(self.training_log_path, 'rb') as f:
                    self.training_log = pickle.load(f)
                print_success("✅ Đã load training log")
            else:
                print_info("📝 Tạo training log mới")
                self.training_log = {
                    'last_training_date': None,
                    'training_history': []
                }
            
            return True
            
        except Exception as e:
            print_warning(f"Lỗi load model: {e}")
            return False
    
    def get_new_data(self, days_back=7):
        """
        Lấy data mới từ database
        
        Parameters:
        days_back: Số ngày gần nhất để lấy (mặc định 7 ngày)
        """
        print_header("LẤY DATA MỚI")
        
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)
            
            # Xác định ngày bắt đầu
            if self.training_log and self.training_log['last_training_date']:
                start_date = self.training_log['last_training_date']
                print_info(f"📅 Lấy data từ ngày: {start_date}")
            else:
                # Nếu chưa có log, lấy 7 ngày gần nhất
                start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
                print_info(f"📅 Lần đầu train incremental, lấy {days_back} ngày gần nhất từ: {start_date}")
            
            # Query data mới
            query = """
                SELECT date, time, results 
                FROM histories_keno 
                WHERE date >= %s
                ORDER BY date ASC, time ASC
            """
            
            cursor.execute(query, (start_date,))
            rows = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            if len(rows) == 0:
                print_warning("❌ Không có data mới")
                return None
            
            # Tạo DataFrame
            df = pd.DataFrame(rows)
            df['results'] = df['results'].apply(lambda x: [int(n) for n in x.split(',')])
            
            # Thống kê
            total_days = df['date'].nunique()
            latest_date = df['date'].max()
            
            print_success(f"✅ Đã lấy {len(df):,} records")
            print_info(f"📊 {total_days} ngày mới (đến {latest_date})")
            
            return df
            
        except Exception as e:
            print_warning(f"Lỗi lấy data: {e}")
            return None
    
    def incremental_train(self, new_data, epochs=20):
        """
        Train incremental với data mới
        
        Parameters:
        new_data: DataFrame chứa data mới
        epochs: Số epochs để train (ít hơn full training)
        """
        print_header("INCREMENTAL TRAINING")
        
        if self.model is None or self.model.model is None:
            print_warning("❌ Chưa load được model hiện có")
            return False
        
        # Tạo features từ data mới
        print_info("🔄 Tạo features từ data mới...")
        X_new, y_new, lengths_new = self.model.create_variable_length_features(new_data)
        
        if len(X_new) == 0:
            print_warning("❌ Không tạo được features từ data mới")
            return False
        
        print_success(f"✅ Tạo được {len(X_new):,} sequences mới")
        
        # Incremental training với learning rate thấp hơn
        print_info("🎯 Bắt đầu incremental training...")
        print_info(f"📚 Epochs: {epochs} (ít hơn full training)")
        print_info("📉 Learning rate: Thấp hơn để fine-tune")
        
        # Giảm learning rate cho incremental training
        current_lr = self.model.model.optimizer.learning_rate.numpy()
        new_lr = current_lr * 0.1  # Giảm 10 lần
        self.model.model.optimizer.learning_rate.assign(new_lr)
        
        print_info(f"🔧 Learning rate: {current_lr:.6f} → {new_lr:.6f}")
        
        # Training
        try:
            # Sử dụng toàn bộ data mới làm training (không chia train/test)
            device_name = '/GPU:0' if len(tf.config.list_physical_devices('GPU')) > 0 else '/CPU:0'
            print_info(f"🚀 Training trên device: {device_name}")
            
            with tf.device(device_name):
                history = self.model.model.fit(
                    X_new, y_new,
                    epochs=epochs,
                    batch_size=16,  # Batch size nhỏ hơn cho incremental
                    verbose=1,
                    validation_split=0.1  # Chỉ 10% validation
                )
            
            print_success("✅ Incremental training hoàn thành!")
            
            # Lưu model updated
            self.model.model.save(self.model_path)
            print_success(f"✅ Đã lưu model updated: {self.model_path}")
            
            # Cập nhật stats
            self._update_stats(new_data, len(X_new), epochs)
            
            # Cập nhật training log
            self._update_training_log(new_data, history)
            
            return True
            
        except Exception as e:
            print_warning(f"❌ Lỗi incremental training: {e}")
            return False
    
    def _update_stats(self, new_data, new_sequences, epochs):
        """Cập nhật stats sau incremental training"""
        if self.stats is None:
            self.stats = {'data_stats': {}, 'model_config': {}}
        
        # Cập nhật data stats
        new_days = new_data['date'].nunique()
        current_days = self.stats.get('data_stats', {}).get('trainable_days', 0)
        
        self.stats['data_stats']['trainable_days'] = current_days + new_days
        self.stats['data_stats']['last_incremental_date'] = new_data['date'].max()
        
        # Cập nhật model config
        current_sequences = self.stats.get('model_config', {}).get('total_sequences', 0)
        self.stats['model_config']['total_sequences'] = current_sequences + new_sequences
        self.stats['model_config']['last_incremental_epochs'] = epochs
        self.stats['model_config']['incremental_trainings'] = self.stats['model_config'].get('incremental_trainings', 0) + 1
        
        # Lưu stats
        with open(self.stats_path, 'wb') as f:
            pickle.dump(self.stats, f)
        
        print_success("✅ Đã cập nhật stats")
    
    def _update_training_log(self, new_data, history):
        """Cập nhật training log"""
        training_record = {
            'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_date_range': f"{new_data['date'].min()} to {new_data['date'].max()}",
            'new_records': len(new_data),
            'new_days': new_data['date'].nunique(),
            'final_loss': float(history.history['loss'][-1]),
            'final_accuracy': float(history.history['accuracy'][-1])
        }
        
        self.training_log['last_training_date'] = new_data['date'].max()
        self.training_log['training_history'].append(training_record)
        
        # Lưu training log
        with open(self.training_log_path, 'wb') as f:
            pickle.dump(self.training_log, f)
        
        print_success("✅ Đã cập nhật training log")
    
    def show_training_history(self):
        """Hiển thị lịch sử training"""
        print_header("LỊCH SỬ INCREMENTAL TRAINING")
        
        if not self.training_log or not self.training_log['training_history']:
            print_warning("Chưa có lịch sử incremental training")
            return
        
        print_info(f"📅 Lần training cuối: {self.training_log['last_training_date']}")
        print_info(f"📊 Tổng số lần incremental: {len(self.training_log['training_history'])}")
        print()
        
        print_info("🕒 LỊCH SỬ CHI TIẾT:")
        for i, record in enumerate(self.training_log['training_history'][-5:], 1):  # 5 lần gần nhất
            print(f"  {i}. {record['date']}")
            print(f"     Data: {record['data_date_range']} ({record['new_days']} ngày)")
            print(f"     Loss: {record['final_loss']:.4f}, Accuracy: {record['final_accuracy']:.4f}")
            print()

def main():
    """Main function cho incremental training"""
    print_header("INCREMENTAL TRAINING SYSTEM")
    print_info("🔄 Chỉ train data mới, không train lại từ đầu")
    print()
    
    trainer = IncrementalTrainer()
    
    # Load model hiện có
    if not trainer.load_existing_model():
        print_warning("❌ Không thể load model hiện có")
        print_info("💡 Hãy chạy full training trước: python train_keno_model.py")
        return
    
    # Hiển thị lịch sử
    trainer.show_training_history()
    
    # Lấy data mới
    new_data = trainer.get_new_data(days_back=7)  # 7 ngày gần nhất
    
    if new_data is None:
        print_info("✅ Không có data mới để train")
        return
    
    # Incremental training
    success = trainer.incremental_train(new_data, epochs=20)
    
    if success:
        print_header("INCREMENTAL TRAINING THÀNH CÔNG")
        print_success("✅ Model đã được cập nhật với data mới!")
        print_info("🔮 Có thể sử dụng ngay: python predict_keno.py")
    else:
        print_warning("❌ Incremental training không thành công")

if __name__ == "__main__":
    main()
