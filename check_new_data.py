#!/usr/bin/env python3
"""
Kiểm tra data mới trong database
"""

import pandas as pd
import pickle
import os
from datetime import datetime, timedelta
from variable_length_model import connect_db, print_header, print_success, print_info, print_warning

def check_new_data():
    """Kiểm tra xem có data mới không"""
    print_header("KIỂM TRA DATA MỚI")
    
    try:
        # Kiểm tra training log
        training_log_path = 'training_log.pkl'
        last_training_date = None
        
        if os.path.exists(training_log_path):
            with open(training_log_path, 'rb') as f:
                training_log = pickle.load(f)
                last_training_date = training_log.get('last_training_date')
                print_info(f"📅 Lần training cuối: {last_training_date}")
        else:
            print_warning("⚠️ Chưa có lịch sử training")
        
        # Kết nối database
        conn = connect_db()
        cursor = conn.cursor(dictionary=True)
        
        # L<PERSON>y thông tin data mới nhất
        cursor.execute("SELECT MAX(date) as latest_date, COUNT(DISTINCT date) as total_days FROM histories_keno")
        result = cursor.fetchone()
        latest_date = result['latest_date']
        total_days = result['total_days']
        
        print_info(f"📊 Data mới nhất trong DB: {latest_date}")
        print_info(f"📈 Tổng số ngày: {total_days}")
        
        # Kiểm tra data mới
        if last_training_date:
            cursor.execute("""
                SELECT COUNT(DISTINCT date) as new_days, COUNT(*) as new_records
                FROM histories_keno 
                WHERE date > %s
            """, (last_training_date,))
            
            new_data = cursor.fetchone()
            new_days = new_data['new_days']
            new_records = new_data['new_records']
            
            if new_days > 0:
                print_success(f"✅ Có {new_days} ngày mới ({new_records:,} records)")
                print_info("💡 Có thể chạy incremental training")
                
                # Hiển thị chi tiết ngày mới
                cursor.execute("""
                    SELECT date, COUNT(*) as records
                    FROM histories_keno 
                    WHERE date > %s
                    GROUP BY date
                    ORDER BY date DESC
                    LIMIT 10
                """, (last_training_date,))
                
                new_days_detail = cursor.fetchall()
                print_info("\n📅 CHI TIẾT NGÀY MỚI:")
                for day in new_days_detail:
                    print(f"   • {day['date']}: {day['records']} records")
                
            else:
                print_info("✅ Không có data mới")
                print_info("💡 Không cần incremental training")
        else:
            print_warning("⚠️ Chưa có lịch sử training, cần full training")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print_warning(f"Lỗi kiểm tra data: {e}")

def show_data_summary():
    """Hiển thị tóm tắt data"""
    print_header("TÓM TẮT DATA")
    
    try:
        conn = connect_db()
        
        # Thống kê tổng quan
        df_summary = pd.read_sql("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT date) as total_days,
                MIN(date) as earliest_date,
                MAX(date) as latest_date
            FROM histories_keno
        """, conn)
        
        print_info("📊 THỐNG KÊ TỔNG QUAN:")
        print(f"   • Tổng records: {df_summary['total_records'].iloc[0]:,}")
        print(f"   • Tổng ngày: {df_summary['total_days'].iloc[0]:,}")
        print(f"   • Từ ngày: {df_summary['earliest_date'].iloc[0]}")
        print(f"   • Đến ngày: {df_summary['latest_date'].iloc[0]}")
        print()
        
        # Thống kê theo ngày gần đây
        df_recent = pd.read_sql("""
            SELECT date, COUNT(*) as records
            FROM histories_keno 
            WHERE date >= DATE_SUB(CURDATE(), INTERVAL 10 DAY)
            GROUP BY date
            ORDER BY date DESC
        """, conn)
        
        if len(df_recent) > 0:
            print_info("📅 10 NGÀY GẦN NHẤT:")
            for _, row in df_recent.iterrows():
                status = "✅" if row['records'] >= 119 else "⚠️"
                print(f"   {status} {row['date']}: {row['records']} records")
        
        conn.close()
        
    except Exception as e:
        print_warning(f"Lỗi hiển thị summary: {e}")

def main():
    """Main function"""
    show_data_summary()
    check_new_data()
    
    print_header("HƯỚNG DẪN")
    print_info("🔄 Nếu có data mới:")
    print("   python incremental_train.py")
    print()
    print_info("🚀 Nếu chưa có model:")
    print("   python train_keno_model.py")
    print()
    print_info("🔮 Để dự đoán:")
    print("   python predict_keno.py")

if __name__ == "__main__":
    main()
