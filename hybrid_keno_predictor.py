#!/usr/bin/env python3
"""
Hybrid Keno Predictor - Kết hợp LSTM + Ng<PERSON><PERSON> hạn (7 kì) + <PERSON>rung hạn (30 kì)
D<PERSON> đoán 6 số trượt với độ chính xác cao nhất
"""

import numpy as np
from datetime import datetime
from enhanced_trend_predictor import EnhancedTrendPredictor
from variable_length_model import connect_db, print_header, print_success, print_info, print_warning, print_error

class HybridKenoPredictor:
    """
    Hybrid Predictor kết hợp:
    1. LSTM Model (keno_variable_length_model.h5)
    2. Trend Analysis ngắn hạn (7 kì gần nhất)
    3. Trend Analysis trung hạn (30 kì gần nhất)
    4. Vector đặc trưng kết hợp
    5. Ensemble prediction với trọng số thông minh
    """
    
    def __init__(self, model_path='keno_variable_length_model.h5'):
        self.model_path = model_path
        
        # Khởi tạo Enhanced Trend Predictor
        self.trend_predictor = EnhancedTrendPredictor(model_path)
        
        # Cấu hình prediction
        self.prediction_config = {
            'default_method': 'ensemble',
            'min_day_draws': 50,  # Tối thiểu 50 kì để dùng LSTM
            'num_predictions': 6,  # Dự đoán 6 số trượt
            'confidence_threshold': 0.7
        }
        
        print_success("✅ Hybrid Keno Predictor đã khởi tạo")
        print_info(f"🎯 Model: {model_path}")
        print_info("🔧 Tích hợp: LSTM + Ngắn hạn (7 kì) + Trung hạn (30 kì)")
    
    def get_latest_day_data(self, target_date=None):
        """Lấy dữ liệu ngày gần nhất từ database"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)
            
            if target_date:
                query = """
                    SELECT date, time, results, period
                    FROM histories_keno
                    WHERE date = %s
                    ORDER BY time ASC
                """
                cursor.execute(query, (target_date,))
            else:
                query = """
                    SELECT date, time, results, period
                    FROM histories_keno
                    WHERE date = (SELECT MAX(date) FROM histories_keno)
                    ORDER BY time ASC
                """
                cursor.execute(query)
            
            rows = cursor.fetchall()
            cursor.close()
            conn.close()
            
            if not rows:
                return None, None
            
            # Chuyển đổi dữ liệu
            date = rows[0]['date']
            day_results = []
            
            for row in rows:
                numbers = [int(n) for n in row['results'].split(',')]
                day_results.append(numbers)
            
            return date, day_results
            
        except Exception as e:
            print_error(f"Lỗi lấy dữ liệu: {e}")
            return None, None
    
    def predict_from_database(self, target_date=None, method='ensemble'):
        """
        Dự đoán từ database với phương pháp hybrid
        
        Parameters:
        - target_date: Ngày cần dự đoán (None = ngày gần nhất)
        - method: 'ensemble', 'lstm', 'short', 'medium'
        """
        print_header("DỰ ĐOÁN HYBRID TỪ DATABASE")
        
        # Lấy dữ liệu ngày
        date, day_results = self.get_latest_day_data(target_date)
        
        if not date or not day_results:
            print_error("Không thể lấy dữ liệu từ database")
            return None
        
        print_info(f"📅 Ngày: {date}")
        print_info(f"📊 Số kì có sẵn: {len(day_results)}")
        
        # Dự đoán
        result = self.predict_missing_numbers(day_results, method=method)
        
        if result:
            result['date'] = date
            result['input_draws'] = len(day_results)
            result['target_draw'] = len(day_results) + 1
            
            # Hiển thị kết quả
            self.display_prediction_result(result)
        
        return result
    
    def predict_missing_numbers(self, day_results=None, method='ensemble'):
        """
        Phương thức chính dự đoán 6 số trượt
        
        Parameters:
        - day_results: Dữ liệu các kì trong ngày (cho LSTM)
        - method: 'ensemble', 'lstm', 'short', 'medium'
        """
        return self.trend_predictor.predict_missing_numbers(
            day_results=day_results,
            num_predictions=self.prediction_config['num_predictions'],
            method=method
        )
    
    def predict_manual(self, day_results, method='ensemble'):
        """Dự đoán từ dữ liệu manual"""
        print_header("DỰ ĐOÁN HYBRID TỪ DỮ LIỆU MANUAL")
        print_info(f"📊 Số kì đầu vào: {len(day_results)}")
        
        result = self.predict_missing_numbers(day_results, method=method)
        
        if result:
            result['input_draws'] = len(day_results)
            result['target_draw'] = len(day_results) + 1
            self.display_prediction_result(result)
        
        return result
    
    def display_prediction_result(self, result):
        """Hiển thị kết quả dự đoán chi tiết"""
        print_header("KẾT QUẢ DỰ ĐOÁN HYBRID")
        
        if 'date' in result:
            print_info(f"📅 Ngày: {result['date']}")
        
        print_info(f"📊 Đã có: {result['input_draws']} kì")
        print_info(f"🎯 Dự đoán: Kỳ {result['target_draw']}")
        print_info(f"🔧 Phương pháp: {result.get('method', 'unknown').upper()}")
        
        # Kết quả chính
        if 'final_predictions' in result:
            predictions = result['final_predictions']
        elif 'predictions' in result:
            predictions = result['predictions']
        else:
            predictions = []
        
        print_success(f"🎲 6 số trượt dự đoán: {predictions}")
        
        # Chi tiết từng phương pháp (nếu là ensemble)
        if result.get('method') == 'ensemble' and 'component_results' in result:
            components = result['component_results']
            print_info("\n📋 Chi tiết từng phương pháp:")
            
            if components.get('lstm') and 'predictions' in components['lstm']:
                print_info(f"🤖 LSTM: {components['lstm']['predictions'][:6]}")
            
            if components.get('medium_term') and 'predictions' in components['medium_term']:
                print_info(f"📈 Trung hạn (30 kì): {components['medium_term']['predictions'][:6]}")
            
            if components.get('short_term') and 'predictions' in components['short_term']:
                print_info(f"⚡ Ngắn hạn (7 kì): {components['short_term']['predictions'][:6]}")
            
            # Trọng số
            if 'weights' in result:
                weights = result['weights']
                print_info(f"\n⚖️ Trọng số: LSTM={weights.get('lstm', 0):.0%}, "
                          f"Trung hạn={weights.get('medium', 0):.0%}, "
                          f"Ngắn hạn={weights.get('short', 0):.0%}")
    
    def analyze_prediction_components(self, day_results=None):
        """Phân tích chi tiết các thành phần dự đoán"""
        print_header("PHÂN TÍCH CHI TIẾT CÁC THÀNH PHẦN DỰ ĐOÁN")
        
        # Load dữ liệu
        if not self.trend_predictor.data:
            if not self.trend_predictor.load_recent_data(100):
                print_error("Không thể load dữ liệu")
                return None
        
        # Phân tích từng thành phần
        print_info("1️⃣ Phân tích xu hướng ngắn hạn (7 kì)...")
        short_trend = self.trend_predictor.analyze_short_term_trend()
        
        print_info("2️⃣ Phân tích xu hướng trung hạn (30 kì)...")
        medium_trend = self.trend_predictor.analyze_medium_term_trend()
        
        print_info("3️⃣ Tạo vector đặc trưng kết hợp...")
        combined_features = self.trend_predictor.create_combined_features()
        
        # Dự đoán từng phương pháp
        print_info("4️⃣ Dự đoán từng phương pháp...")
        
        results = {}
        
        # LSTM (nếu có đủ dữ liệu ngày)
        if day_results and len(day_results) >= 50:
            results['lstm'] = self.trend_predictor.predict_lstm_only(day_results, 6)
        
        results['short'] = self.trend_predictor.predict_short_term(6)
        results['medium'] = self.trend_predictor.predict_medium_term(6)
        results['ensemble'] = self.trend_predictor.ensemble_prediction(day_results, 6)
        
        return {
            'trends': {
                'short_term': short_trend,
                'medium_term': medium_trend,
                'combined_features': combined_features
            },
            'predictions': results
        }
    
    def show_model_info(self):
        """Hiển thị thông tin model và cấu hình"""
        print_header("THÔNG TIN HYBRID KENO PREDICTOR")
        
        print_info(f"🎯 Model path: {self.model_path}")
        print_info(f"🔧 Phương pháp mặc định: {self.prediction_config['default_method']}")
        print_info(f"📊 Số dự đoán: {self.prediction_config['num_predictions']}")
        print_info(f"📈 Tối thiểu kì/ngày cho LSTM: {self.prediction_config['min_day_draws']}")
        
        print_info("\n🧠 Các phương pháp hỗ trợ:")
        print_info("  • ensemble: Kết hợp LSTM + ngắn hạn + trung hạn")
        print_info("  • lstm: Chỉ dùng LSTM model")
        print_info("  • short: Chỉ dùng xu hướng ngắn hạn (7 kì)")
        print_info("  • medium: Chỉ dùng xu hướng trung hạn (30 kì)")
        
        print_info("\n⚖️ Trọng số ensemble:")
        print_info("  • LSTM: 40%")
        print_info("  • Trung hạn (30 kì): 35%")
        print_info("  • Ngắn hạn (7 kì): 25%")


def main():
    """Demo function"""
    print_header("HYBRID KENO PREDICTOR - DEMO")
    
    try:
        # Khởi tạo predictor
        predictor = HybridKenoPredictor()
        
        while True:
            print("\n" + "="*60)
            print("🎯 HYBRID KENO PREDICTOR")
            print("="*60)
            print("1. Dự đoán Ensemble (LSTM + Ngắn hạn + Trung hạn)")
            print("2. Dự đoán chỉ LSTM")
            print("3. Dự đoán chỉ Ngắn hạn (7 kì)")
            print("4. Dự đoán chỉ Trung hạn (30 kì)")
            print("5. Phân tích chi tiết các thành phần")
            print("6. Dự đoán ngày cụ thể")
            print("7. Thông tin model")
            print("8. Thoát")
            
            choice = input("\nChọn chức năng (1-8): ").strip()
            
            if choice == '1':
                predictor.predict_from_database(method='ensemble')
            
            elif choice == '2':
                predictor.predict_from_database(method='lstm')
            
            elif choice == '3':
                predictor.predict_from_database(method='short')
            
            elif choice == '4':
                predictor.predict_from_database(method='medium')
            
            elif choice == '5':
                date, day_results = predictor.get_latest_day_data()
                if date and day_results:
                    predictor.analyze_prediction_components(day_results)
                else:
                    print_error("Không thể lấy dữ liệu")
            
            elif choice == '6':
                date = input("Nhập ngày (YYYY-MM-DD): ").strip()
                method = input("Phương pháp (ensemble/lstm/short/medium): ").strip() or 'ensemble'
                predictor.predict_from_database(date, method)
            
            elif choice == '7':
                predictor.show_model_info()
            
            elif choice == '8':
                print_info("Thoát Hybrid Predictor")
                break
            
            else:
                print_warning("Lựa chọn không hợp lệ")
    
    except KeyboardInterrupt:
        print_info("\nThoát chương trình")
    except Exception as e:
        print_error(f"Lỗi: {e}")


if __name__ == "__main__":
    main()
