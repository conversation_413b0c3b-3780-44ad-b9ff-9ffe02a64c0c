#!/usr/bin/env python3
"""
Demo Hybrid Prediction - Test tính năng dự đoán ngắn hạn và trung hạn
Kết hợp với LSTM model để dự đoán 6 số trượt
"""

import numpy as np
from datetime import datetime, timedelta
from hybrid_keno_predictor import HybridKenoPredictor
from variable_length_model import connect_db, print_header, print_success, print_info, print_warning, print_error

def test_all_prediction_methods():
    """Test tất cả các phương pháp dự đoán"""
    print_header("TEST TẤT CẢ PHƯƠNG PHÁP DỰ ĐOÁN")
    
    # Khởi tạo predictor
    predictor = HybridKenoPredictor()
    
    # Lấy dữ liệu ngày gần nhất
    date, day_results = predictor.get_latest_day_data()
    
    if not date or not day_results:
        print_error("Không thể lấy dữ liệu test")
        return
    
    print_info(f"📅 Test với ngày: {date}")
    print_info(f"📊 Số kì có sẵn: {len(day_results)}")
    
    # Test từng phương pháp
    methods = ['ensemble', 'lstm', 'short', 'medium']
    results = {}
    
    for method in methods:
        print_header(f"TEST PHƯƠNG PHÁP: {method.upper()}")
        
        try:
            result = predictor.predict_missing_numbers(day_results, method=method)
            
            if result:
                if 'final_predictions' in result:
                    predictions = result['final_predictions']
                elif 'predictions' in result:
                    predictions = result['predictions']
                else:
                    predictions = []
                
                results[method] = predictions
                print_success(f"✅ {method.upper()}: {predictions}")
            else:
                print_warning(f"⚠️ {method.upper()}: Không có kết quả")
                results[method] = []
                
        except Exception as e:
            print_error(f"❌ {method.upper()}: Lỗi - {e}")
            results[method] = []
    
    # So sánh kết quả
    print_header("SO SÁNH KẾT QUẢ")
    
    for method, predictions in results.items():
        print_info(f"{method.upper():10}: {predictions}")
    
    # Tìm số xuất hiện nhiều nhất
    all_numbers = []
    for predictions in results.values():
        all_numbers.extend(predictions)
    
    if all_numbers:
        from collections import Counter
        number_counts = Counter(all_numbers)
        most_common = number_counts.most_common(6)
        
        print_info(f"\n🎯 Số xuất hiện nhiều nhất: {[num for num, count in most_common]}")
        print_info("📊 Tần suất xuất hiện:")
        for num, count in most_common:
            print_info(f"   Số {num}: {count}/{len(methods)} phương pháp")
    
    return results

def test_trend_analysis():
    """Test phân tích xu hướng ngắn hạn và trung hạn"""
    print_header("TEST PHÂN TÍCH XU HƯỚNG")
    
    predictor = HybridKenoPredictor()
    
    # Load dữ liệu
    if not predictor.trend_predictor.load_recent_data(100):
        print_error("Không thể load dữ liệu")
        return
    
    print_info("✅ Đã load dữ liệu thành công")
    
    # Test xu hướng ngắn hạn
    print_header("XU HƯỚNG NGẮN HẠN (7 KÌ)")
    short_trend = predictor.trend_predictor.analyze_short_term_trend()
    
    if short_trend:
        print_success("✅ Phân tích xu hướng ngắn hạn thành công")
        
        # Hiển thị top 10 số có missing rate cao nhất
        number_stats = short_trend['number_stats']
        sorted_by_missing = sorted(number_stats.items(), 
                                 key=lambda x: x[1]['missing_rate'], reverse=True)
        
        print_info("🔝 Top 10 số trượt nhiều nhất (7 kì):")
        for i, (num, stats) in enumerate(sorted_by_missing[:10]):
            print_info(f"   {i+1:2}. Số {num:2}: {stats['missing_rate']:.1f}% trượt, "
                      f"gap={stats['gap_from_last']}, momentum={stats['momentum']:.1f}")
    
    # Test xu hướng trung hạn
    print_header("XU HƯỚNG TRUNG HẠN (30 KÌ)")
    medium_trend = predictor.trend_predictor.analyze_medium_term_trend()
    
    if medium_trend:
        print_success("✅ Phân tích xu hướng trung hạn thành công")
        
        # Hiển thị top 10 số có missing rate cao nhất
        number_stats = medium_trend['number_stats']
        sorted_by_missing = sorted(number_stats.items(), 
                                 key=lambda x: x[1]['missing_rate'], reverse=True)
        
        print_info("🔝 Top 10 số trượt nhiều nhất (30 kì):")
        for i, (num, stats) in enumerate(sorted_by_missing[:10]):
            print_info(f"   {i+1:2}. Số {num:2}: {stats['missing_rate']:.1f}% trượt, "
                      f"trend={stats['trend']:+.1f}, stability={stats['stability']:.1f}")
    
    # Test vector đặc trưng kết hợp
    print_header("VECTOR ĐẶC TRƯNG KẾT HỢP")
    combined_features = predictor.trend_predictor.create_combined_features()
    
    if combined_features:
        print_success("✅ Tạo vector đặc trưng thành công")
        
        features = combined_features['features']
        feature_names = combined_features['feature_names']
        
        print_info(f"📊 Vector đặc trưng 8 chiều cho {len(features)} số")
        print_info(f"🏷️ Tên features: {feature_names}")
        
        # Hiển thị vector cho 5 số đầu
        print_info("📋 Vector mẫu (5 số đầu):")
        for num in range(1, 6):
            if num in features:
                vector = features[num]
                print_info(f"   Số {num}: {vector}")

def test_ensemble_prediction():
    """Test ensemble prediction chi tiết"""
    print_header("TEST ENSEMBLE PREDICTION CHI TIẾT")
    
    predictor = HybridKenoPredictor()
    
    # Lấy dữ liệu ngày
    date, day_results = predictor.get_latest_day_data()
    
    if not date or not day_results:
        print_error("Không thể lấy dữ liệu")
        return
    
    print_info(f"📅 Ngày test: {date}")
    print_info(f"📊 Số kì: {len(day_results)}")
    
    # Ensemble prediction
    result = predictor.trend_predictor.ensemble_prediction(day_results, 6)
    
    if not result:
        print_error("Ensemble prediction thất bại")
        return
    
    print_success("✅ Ensemble prediction thành công")
    
    # Hiển thị chi tiết
    print_header("CHI TIẾT ENSEMBLE RESULT")
    
    # Kết quả cuối
    final_predictions = result.get('final_predictions', [])
    final_scores = result.get('final_scores', {})
    
    print_info("🎯 Kết quả cuối cùng:")
    for i, num in enumerate(final_predictions):
        score = final_scores.get(num, 0)
        print_info(f"   {i+1}. Số {num}: {score:.2f} điểm")
    
    # Kết quả từng thành phần
    components = result.get('component_results', {})
    weights = result.get('weights', {})
    
    print_info(f"\n⚖️ Trọng số: {weights}")
    
    print_info("\n📋 Kết quả từng thành phần:")
    
    if 'lstm' in components and components['lstm']:
        lstm_preds = components['lstm'].get('predictions', [])
        print_info(f"🤖 LSTM ({weights.get('lstm', 0):.0%}): {lstm_preds[:6]}")
    
    if 'medium_term' in components and components['medium_term']:
        medium_preds = components['medium_term'].get('predictions', [])
        print_info(f"📈 Trung hạn ({weights.get('medium', 0):.0%}): {medium_preds[:6]}")
    
    if 'short_term' in components and components['short_term']:
        short_preds = components['short_term'].get('predictions', [])
        print_info(f"⚡ Ngắn hạn ({weights.get('short', 0):.0%}): {short_preds[:6]}")

def test_multiple_days():
    """Test dự đoán nhiều ngày"""
    print_header("TEST DỰ ĐOÁN NHIỀU NGÀY")
    
    predictor = HybridKenoPredictor()
    
    # Lấy 5 ngày gần nhất
    try:
        conn = connect_db()
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT DISTINCT date
            FROM histories_keno
            ORDER BY date DESC
            LIMIT 5
        """
        
        cursor.execute(query)
        dates = [row['date'] for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        print_info(f"📅 Test với {len(dates)} ngày gần nhất")
        
        results = {}
        
        for date in dates:
            print_header(f"TEST NGÀY: {date}")
            
            try:
                result = predictor.predict_from_database(target_date=date, method='ensemble')
                
                if result and 'final_predictions' in result:
                    predictions = result['final_predictions']
                    results[date] = predictions
                    print_success(f"✅ {date}: {predictions}")
                else:
                    print_warning(f"⚠️ {date}: Không có kết quả")
                    results[date] = []
                    
            except Exception as e:
                print_error(f"❌ {date}: Lỗi - {e}")
                results[date] = []
        
        # Tổng hợp kết quả
        print_header("TỔNG HỢP KẾT QUẢ NHIỀU NGÀY")
        
        for date, predictions in results.items():
            print_info(f"{date}: {predictions}")
        
        # Tìm số xuất hiện thường xuyên
        all_predictions = []
        for predictions in results.values():
            all_predictions.extend(predictions)
        
        if all_predictions:
            from collections import Counter
            number_counts = Counter(all_predictions)
            most_common = number_counts.most_common(10)
            
            print_info(f"\n🎯 Top 10 số được dự đoán nhiều nhất:")
            for num, count in most_common:
                print_info(f"   Số {num}: {count}/{len(dates)} ngày ({count/len(dates)*100:.1f}%)")
        
    except Exception as e:
        print_error(f"Lỗi test nhiều ngày: {e}")

def main():
    """Main demo function"""
    print_header("DEMO HYBRID PREDICTION - TEST TÍNH NĂNG MỚI")
    
    while True:
        print("\n" + "="*60)
        print("🧪 DEMO HYBRID PREDICTION")
        print("="*60)
        print("1. Test tất cả phương pháp dự đoán")
        print("2. Test phân tích xu hướng (7 kì + 30 kì)")
        print("3. Test ensemble prediction chi tiết")
        print("4. Test dự đoán nhiều ngày")
        print("5. Chạy Hybrid Predictor chính")
        print("6. Thoát")
        
        choice = input("\nChọn test (1-6): ").strip()
        
        try:
            if choice == '1':
                test_all_prediction_methods()
            
            elif choice == '2':
                test_trend_analysis()
            
            elif choice == '3':
                test_ensemble_prediction()
            
            elif choice == '4':
                test_multiple_days()
            
            elif choice == '5':
                from hybrid_keno_predictor import main as hybrid_main
                hybrid_main()
            
            elif choice == '6':
                print_info("Thoát demo")
                break
            
            else:
                print_warning("Lựa chọn không hợp lệ")
        
        except KeyboardInterrupt:
            print_info("\nThoát test")
            break
        except Exception as e:
            print_error(f"Lỗi test: {e}")


if __name__ == "__main__":
    main()
