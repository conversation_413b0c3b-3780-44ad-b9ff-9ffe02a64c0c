#!/usr/bin/env python3
"""
Prediction chính cho Variable Length Keno Model
"""

import tensorflow as tf
import pandas as pd
import pickle
from variable_length_model import VariableLengthKenoModel, connect_db, print_header, print_success, print_info, print_warning

class KenoPredictor:
    """
    Predictor chính cho Keno với Variable Length Model
    """

    def __init__(self, model_path='keno_variable_length_model.h5'):
        self.model_path = model_path
        self.model = None
        self.stats = None
        self.load_model_and_stats()

    def load_model_and_stats(self):
        """Load model và stats"""
        try:
            # Load model
            self.model = VariableLengthKenoModel()
            self.model.model = tf.keras.models.load_model(self.model_path)
            print_success(f"✅ Đã load model từ {self.model_path}")

            # Load stats
            try:
                with open('model_stats.pkl', 'rb') as f:
                    self.stats = pickle.load(f)
                print_success("✅ Đã load thống kê model")
            except:
                print_warning("⚠️ Không load được stats")

        except Exception as e:
            print_warning(f"❌ Không thể load model: {e}")

    def get_latest_day_data(self, target_date=None):
        """Lấy dữ liệu ngày gần nhất từ database"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            if target_date is None:
                # Lấy ngày gần nhất
                cursor.execute("SELECT MAX(date) as max_date FROM histories_keno")
                result = cursor.fetchone()
                target_date = result['max_date']

            # Lấy tất cả kỳ của ngày đó
            query = """
                SELECT time, results FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """
            cursor.execute(query, (target_date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) == 0:
                print_warning(f"Không có dữ liệu cho ngày {target_date}")
                return None, None

            # Chuyển đổi results
            day_results = []
            for row in rows:
                numbers = [int(n) for n in row['results'].split(',')]
                day_results.append(numbers)

            print_success(f"✅ Đã lấy {len(day_results)} kỳ của ngày {target_date}")
            return target_date, day_results

        except Exception as e:
            print_warning(f"Lỗi lấy dữ liệu: {e}")
            return None, None

    def predict_next_draw(self, day_results, show_details=True, use_hybrid=True):
        """
        Dự đoán kỳ tiếp theo từ TẤT CẢ kỳ của ngày

        Parameters:
        day_results: List tất cả kỳ của ngày
        show_details: Hiển thị chi tiết
        use_hybrid: Sử dụng hybrid (LSTM + std analysis)

        Returns:
        dict: Kết quả dự đoán
        """
        if self.model is None or self.model.model is None:
            print_warning("Model chưa được load")
            return None

        if len(day_results) < 50:
            print_warning(f"Cần ít nhất 50 kỳ, hiện có {len(day_results)}")
            return None

        if show_details:
            print_header("DỰ ĐOÁN KỲ TIẾP THEO")
            print_info(f"🎯 Logic: Dùng TẤT CẢ {len(day_results)} kỳ để dự đoán kỳ {len(day_results)+1}")
            if use_hybrid:
                print_info("🔄 Sử dụng Hybrid: LSTM + Std Analysis")

        # Dự đoán với hybrid hoặc LSTM only
        prediction_results = self.model.predict_missing_numbers(
            day_results, num_miss=6, use_hybrid=use_hybrid
        )

        # Xử lý kết quả
        if use_hybrid and isinstance(prediction_results, dict):
            # Hybrid results
            miss_numbers = prediction_results.get('ensemble', [])
            lstm_numbers = prediction_results.get('lstm', [])
            std_numbers = prediction_results.get('std_analysis', [])

            result = {
                'input_draws': len(day_results),
                'target_draw': len(day_results) + 1,
                'hybrid_results': prediction_results,
                'miss_numbers': miss_numbers,  # Ensemble result
                'lstm_numbers': lstm_numbers,
                'std_numbers': std_numbers
            }

            if show_details:
                print_success(f"✅ Hybrid prediction hoàn thành!")
                print_info(f"🎯 Ensemble (khuyến nghị): {miss_numbers}")
                print_info(f"🧠 LSTM only: {lstm_numbers}")
                print_info(f"📊 Std analysis: {std_numbers}")

        else:
            # LSTM only results
            miss_numbers = prediction_results if isinstance(prediction_results, list) else []

            result = {
                'input_draws': len(day_results),
                'target_draw': len(day_results) + 1,
                'miss_numbers': miss_numbers,
                'method': 'lstm_only'
            }

            if show_details:
                print_success(f"✅ LSTM prediction hoàn thành!")
                print_info(f"🎯 6 số trượt: {miss_numbers}")

        # Thêm LSTM probabilities nếu cần
        try:
            probabilities = self.model.predict_next_draw(day_results)
            if probabilities is not None:
                prob_list = [(i + 1, prob) for i, prob in enumerate(probabilities)]
                prob_list.sort(key=lambda x: x[1], reverse=True)

                result['most_likely'] = [num for num, prob in prob_list[:10]]
                result['least_likely'] = [num for num, prob in prob_list[-10:]]
                result['all_probabilities'] = prob_list
        except:
            pass

        return result

    def predict_from_database(self, target_date=None):
        """Dự đoán từ database"""
        print_header("DỰ ĐOÁN TỪ DATABASE")

        # Lấy dữ liệu
        date, day_results = self.get_latest_day_data(target_date)

        if date is None or day_results is None:
            return None

        # Dự đoán
        result = self.predict_next_draw(day_results)

        if result:
            result['date'] = date

            print_header("KẾT QUẢ DỰ ĐOÁN")
            print_info(f"📅 Ngày: {date}")
            print_info(f"📊 Đã có: {result['input_draws']} kỳ")
            print_info(f"🎯 Dự đoán: Kỳ {result['target_draw']}")
            print_success(f"🎲 6 số trượt: {result['miss_numbers']}")

        return result

    def predict_manual(self, day_results):
        """Dự đoán từ dữ liệu manual"""
        print_header("DỰ ĐOÁN TỪ DỮ LIỆU MANUAL")

        return self.predict_next_draw(day_results)

    def show_model_info(self):
        """Hiển thị thông tin model"""
        print_header("THÔNG TIN MODEL")

        if self.stats:
            data_stats = self.stats.get('data_stats', {})
            model_config = self.stats.get('model_config', {})

            print_info("📊 THỐNG KÊ TRAINING:")
            print(f"   • Tổng ngày train: {data_stats.get('trainable_days', 'N/A')}")
            print(f"   • Ngày đầy đủ (119 kỳ): {data_stats.get('full_days', 'N/A')}")
            print(f"   • Tổng sequences: {model_config.get('total_sequences', 'N/A'):,}")
            print(f"   • Độ dài sequence TB: {model_config.get('avg_sequence_length', 'N/A'):.1f} kỳ")
            print(f"   • Epochs trained: {model_config.get('epochs_trained', 'N/A')}")

            print_info("⚙️ CẤU HÌNH MODEL:")
            print(f"   • Max sequence length: {model_config.get('max_sequence_length', 'N/A')}")
            print(f"   • Draws per day: {model_config.get('draws_per_day', 'N/A')}")
        else:
            print_warning("Không có thông tin stats")

def demo_prediction():
    """Demo prediction"""
    print_header("DEMO PREDICTION")

    predictor = KenoPredictor()

    if predictor.model is None:
        print_warning("Không thể load model. Hãy train model trước:")
        print_info("python train_keno_model.py")
        return

    # Hiển thị thông tin model
    predictor.show_model_info()

    # Demo với database
    print_info("\n🔮 Demo dự đoán từ database...")
    result = predictor.predict_from_database()

    # Demo với dữ liệu manual
    print_info("\n🔮 Demo dự đoán với dữ liệu manual...")

    # Tạo ngày mẫu với 80 kỳ
    sample_day = []
    for i in range(80):
        numbers = list(range(1 + i % 15, 81, 3))[:20]
        sample_day.append(numbers)

    manual_result = predictor.predict_manual(sample_day)

    if manual_result:
        print_success("✅ Demo thành công!")

def interactive_prediction():
    """Prediction tương tác"""
    print_header("PREDICTION TƯƠNG TÁC")

    predictor = KenoPredictor()

    if predictor.model is None:
        print_warning("Không thể load model")
        return

    while True:
        print_info("\nChọn loại prediction:")
        print("1. Hybrid prediction (LSTM + Std) - Khuyến nghị")
        print("2. LSTM only prediction")
        print("3. Dự đoán ngày cụ thể")
        print("4. Hiển thị thông tin model")
        print("5. Thoát")

        try:
            choice = input("\nNhập lựa chọn (1-5): ").strip()

            if choice == '1':
                print_info("🔄 Hybrid Prediction (LSTM + Std Analysis)")
                result = predictor.predict_from_database()
                if result and 'hybrid_results' in result:
                    print_header("CHI TIẾT HYBRID")
                    hybrid = result['hybrid_results']
                    print_info(f"🧠 LSTM: {hybrid.get('lstm', [])}")
                    print_info(f"📊 Std Analysis: {hybrid.get('std_analysis', [])}")
                    print_success(f"🎯 Ensemble: {hybrid.get('ensemble', [])}")

            elif choice == '2':
                print_info("🧠 LSTM Only Prediction")
                # Tạm thời modify để force LSTM only
                original_method = predictor.predict_next_draw
                def lstm_only_wrapper(day_results, show_details=True, use_hybrid=False):
                    return original_method(day_results, show_details, use_hybrid=False)
                predictor.predict_next_draw = lstm_only_wrapper
                predictor.predict_from_database()
                predictor.predict_next_draw = original_method

            elif choice == '3':
                date = input("Nhập ngày (YYYY-MM-DD): ").strip()
                predictor.predict_from_database(date)

            elif choice == '4':
                predictor.show_model_info()

            elif choice == '5':
                print_info("Thoát prediction")
                break

            else:
                print_warning("Lựa chọn không hợp lệ")

        except KeyboardInterrupt:
            print_info("\nThoát prediction")
            break
        except Exception as e:
            print_warning(f"Lỗi: {e}")

def main():
    """Main function"""
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == 'demo':
            demo_prediction()
        elif sys.argv[1] == 'interactive':
            interactive_prediction()
        else:
            print_warning(f"Unknown option: {sys.argv[1]}")
            print_info("Usage: python predict_keno.py [demo|interactive]")
    else:
        # Default: interactive
        interactive_prediction()

if __name__ == "__main__":
    main()
