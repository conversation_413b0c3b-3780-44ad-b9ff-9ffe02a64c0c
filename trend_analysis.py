#!/usr/bin/env python3
"""
Phân tích xu hướng dài hạn (70 kì) và ngắn hạn (30 kì) cho dữ liệu Keno
Tạo vector đặc trưng và dự đoán 6 số trượt
"""

import numpy as np
import pandas as pd
import mysql.connector
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

# Hàm in thông báo
def print_header(message):
    print(f"\n\033[1;36m{'='*20} {message} {'='*20}\033[0m")

def print_success(message):
    print(f"\033[1;32m✓ {message}\033[0m")

def print_info(message):
    print(f"\033[1;34m→ {message}\033[0m")

def print_warning(message):
    print(f"\033[1;33m⚠ {message}\033[0m")

def print_error(message):
    print(f"\033[1;31m✗ {message}\033[0m")

# Kết nối database
def connect_db():
    return mysql.connector.connect(
        host="localhost",
        port=3307,
        user="root",
        password="1",
        database="keno"
    )

class TrendAnalyzer:
    """
    Phân tích xu hướng Keno với:
    - Xu hướng trung hạn: 30 kì (theo yêu cầu)
    - Xu hướng ngắn hạn: 7 kì (theo yêu cầu)
    - Vector đặc trưng cho 30 kì và 7 kì
    - Dự đoán 6 số trượt kết hợp với LSTM model
    """

    def __init__(self):
        self.data = None
        self.medium_term_period = 30  # Xu hướng trung hạn (theo yêu cầu)
        self.short_term_period = 7   # Xu hướng ngắn hạn (theo yêu cầu)
        self.feature_periods = [30, 7]  # Vector đặc trưng cho 30 kì và 7 kì
        self.missing_count = 6       # Số lượng số trượt dự đoán

        # Thêm period dài hạn để tương thích với code cũ
        self.long_term_period = 70   # Giữ để tương thích

    def load_data_3_months(self):
        """Tải dữ liệu 3 tháng gần nhất"""
        print_header("TẢI DỮ LIỆU 3 THÁNG GẦN NHẤT")

        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            # Tính ngày 3 tháng trước
            three_months_ago = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')

            query = """
                SELECT date, time, results, period
                FROM histories_keno
                WHERE date >= %s
                ORDER BY date ASC, time ASC
            """

            print_info(f"Truy vấn dữ liệu từ ngày: {three_months_ago}")
            cursor.execute(query, (three_months_ago,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) == 0:
                print_error("Không có dữ liệu trong 3 tháng gần nhất")
                return False

            # Chuyển đổi dữ liệu
            self.data = pd.DataFrame(rows)
            self.data['results'] = self.data['results'].apply(
                lambda x: [int(n) for n in x.split(',')]
            )

            # Xử lý datetime đúng cách
            try:
                # Chuyển đổi date và time thành string trước
                self.data['date_str'] = self.data['date'].astype(str)
                self.data['time_str'] = self.data['time'].astype(str)

                # Kết hợp date và time
                self.data['datetime'] = pd.to_datetime(
                    self.data['date_str'] + ' ' + self.data['time_str'],
                    format='%Y-%m-%d %H:%M:%S'
                )
            except Exception as e:
                print_warning(f"Lỗi xử lý datetime: {e}")
                # Fallback: chỉ sử dụng date
                self.data['datetime'] = pd.to_datetime(self.data['date'])

            print_success(f"✅ Đã tải {len(self.data):,} records")
            print_info(f"📅 Từ {self.data['date'].min()} đến {self.data['date'].max()}")
            print_info(f"📊 Số ngày: {self.data['date'].nunique()}")

            return True

        except Exception as e:
            print_error(f"Lỗi tải dữ liệu: {e}")
            return False

    def analyze_long_term_trend(self):
        """Phân tích xu hướng dài hạn 70 kì"""
        print_header(f"PHÂN TÍCH XU HƯỚNG DÀI HẠN ({self.long_term_period} KÌ)")

        if len(self.data) < self.long_term_period:
            print_warning(f"Không đủ dữ liệu cho phân tích {self.long_term_period} kì")
            return None

        # Lấy 70 kì gần nhất
        recent_data = self.data.tail(self.long_term_period).copy()

        # Thống kê tần suất xuất hiện
        number_frequency = defaultdict(int)
        total_draws = len(recent_data)

        for _, row in recent_data.iterrows():
            for num in row['results']:
                number_frequency[num] += 1

        # Tính tỷ lệ phần trăm
        frequency_stats = {}
        for num in range(1, 81):
            count = number_frequency[num]
            percentage = (count / total_draws) * 100
            frequency_stats[num] = {
                'count': count,
                'percentage': percentage,
                'expected': total_draws * 0.25,  # Kỳ vọng 25% (20/80 số)
                'deviation': count - (total_draws * 0.25)
            }

        # Phân loại số theo xu hướng
        hot_numbers = []    # Số nóng (xuất hiện nhiều)
        cold_numbers = []   # Số lạnh (xuất hiện ít)
        stable_numbers = [] # Số ổn định

        for num in range(1, 81):
            stats = frequency_stats[num]
            if stats['percentage'] > 27:  # Trên 27%
                hot_numbers.append(num)
            elif stats['percentage'] < 23:  # Dưới 23%
                cold_numbers.append(num)
            else:
                stable_numbers.append(num)

        print_info(f"📊 Phân tích {total_draws} kì gần nhất:")
        print_info(f"🔥 Số nóng (>27%): {len(hot_numbers)} số")
        print_info(f"❄️ Số lạnh (<23%): {len(cold_numbers)} số")
        print_info(f"⚖️ Số ổn định (23-27%): {len(stable_numbers)} số")

        # Top 10 số nóng và lạnh nhất
        sorted_by_freq = sorted(frequency_stats.items(),
                               key=lambda x: x[1]['percentage'], reverse=True)

        print_info("\n🔥 TOP 10 SỐ NÓNG NHẤT:")
        for i, (num, stats) in enumerate(sorted_by_freq[:10]):
            print(f"   {i+1:2d}. Số {num:2d}: {stats['count']:2d} lần ({stats['percentage']:.1f}%)")

        print_info("\n❄️ TOP 10 SỐ LẠNH NHẤT:")
        for i, (num, stats) in enumerate(sorted_by_freq[-10:]):
            print(f"   {i+1:2d}. Số {num:2d}: {stats['count']:2d} lần ({stats['percentage']:.1f}%)")

        return {
            'frequency_stats': frequency_stats,
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers,
            'stable_numbers': stable_numbers,
            'period': self.long_term_period
        }

    def analyze_medium_term_trend(self):
        """Phân tích xu hướng trung hạn 30 kì"""
        print_header(f"PHÂN TÍCH XU HƯỚNG TRUNG HẠN ({self.medium_term_period} KÌ)")

        if len(self.data) < self.medium_term_period:
            print_warning(f"Không đủ dữ liệu cho phân tích {self.medium_term_period} kì")
            return None

        # Lấy 30 kì gần nhất
        recent_data = self.data.tail(self.medium_term_period).copy()

        # Thống kê tần suất
        number_frequency = defaultdict(int)
        total_draws = len(recent_data)

        for _, row in recent_data.iterrows():
            for num in row['results']:
                number_frequency[num] += 1

        # Phân tích xu hướng tăng/giảm
        # Chia thành 2 nửa để so sánh
        half_point = total_draws // 2
        first_half = recent_data.iloc[:half_point]
        second_half = recent_data.iloc[half_point:]

        first_half_freq = defaultdict(int)
        second_half_freq = defaultdict(int)

        for _, row in first_half.iterrows():
            for num in row['results']:
                first_half_freq[num] += 1

        for _, row in second_half.iterrows():
            for num in row['results']:
                second_half_freq[num] += 1

        # Tính xu hướng
        trend_stats = {}
        for num in range(1, 81):
            first_rate = (first_half_freq[num] / len(first_half)) * 100
            second_rate = (second_half_freq[num] / len(second_half)) * 100
            trend = second_rate - first_rate

            trend_stats[num] = {
                'first_half_rate': first_rate,
                'second_half_rate': second_rate,
                'trend': trend,
                'total_count': number_frequency[num],
                'total_rate': (number_frequency[num] / total_draws) * 100
            }

        # Phân loại xu hướng
        trending_up = []    # Xu hướng tăng
        trending_down = []  # Xu hướng giảm
        stable_trend = []   # Ổn định

        for num in range(1, 81):
            trend = trend_stats[num]['trend']
            if trend > 5:  # Tăng > 5%
                trending_up.append(num)
            elif trend < -5:  # Giảm > 5%
                trending_down.append(num)
            else:
                stable_trend.append(num)

        print_info(f"📊 Phân tích xu hướng {total_draws} kì gần nhất:")
        print_info(f"📈 Xu hướng tăng: {len(trending_up)} số")
        print_info(f"📉 Xu hướng giảm: {len(trending_down)} số")
        print_info(f"➡️ Ổn định: {len(stable_trend)} số")

        # Top xu hướng
        sorted_by_trend = sorted(trend_stats.items(),
                                key=lambda x: x[1]['trend'], reverse=True)

        print_info("\n📈 TOP 10 XU HƯỚNG TĂNG MẠNH:")
        for i, (num, stats) in enumerate(sorted_by_trend[:10]):
            print(f"   {i+1:2d}. Số {num:2d}: {stats['trend']:+.1f}% ({stats['total_rate']:.1f}%)")

        print_info("\n📉 TOP 10 XU HƯỚNG GIẢM MẠNH:")
        for i, (num, stats) in enumerate(sorted_by_trend[-10:]):
            print(f"   {i+1:2d}. Số {num:2d}: {stats['trend']:+.1f}% ({stats['total_rate']:.1f}%)")

        return {
            'trend_stats': trend_stats,
            'trending_up': trending_up,
            'trending_down': trending_down,
            'stable_trend': stable_trend,
            'period': self.medium_term_period
        }

    def analyze_short_term_trend(self):
        """Phân tích xu hướng ngắn hạn 7 kì"""
        print_header(f"PHÂN TÍCH XU HƯỚNG NGẮN HẠN ({self.short_term_period} KÌ)")

        if len(self.data) < self.short_term_period:
            print_warning(f"Không đủ dữ liệu cho phân tích {self.short_term_period} kì")
            return None

        # Lấy 7 kì gần nhất
        recent_data = self.data.tail(self.short_term_period).copy()

        # Thống kê tần suất xuất hiện trong 7 kì
        number_frequency = defaultdict(int)
        total_draws = len(recent_data)

        for _, row in recent_data.iterrows():
            for num in row['results']:
                number_frequency[num] += 1

        # Với 7 kì, phân tích theo pattern khác
        # Phân tích 3 kì đầu vs 4 kì cuối
        first_part = recent_data.iloc[:3]  # 3 kì đầu
        second_part = recent_data.iloc[3:]  # 4 kì cuối

        first_part_freq = defaultdict(int)
        second_part_freq = defaultdict(int)

        for _, row in first_part.iterrows():
            for num in row['results']:
                first_part_freq[num] += 1

        for _, row in second_part.iterrows():
            for num in row['results']:
                second_part_freq[num] += 1

        # Tính xu hướng ngắn hạn
        trend_stats = {}
        for num in range(1, 81):
            first_rate = (first_part_freq[num] / len(first_part)) * 100 if len(first_part) > 0 else 0
            second_rate = (second_part_freq[num] / len(second_part)) * 100 if len(second_part) > 0 else 0
            trend = second_rate - first_rate

            # Tính momentum (xu hướng gần đây)
            recent_appearances = []
            for i, (_, row) in enumerate(recent_data.iterrows()):
                if num in row['results']:
                    recent_appearances.append(i)

            # Momentum: số lần xuất hiện trong 3 kì cuối / tổng số lần
            momentum = 0
            if number_frequency[num] > 0:
                recent_count = sum(1 for i in recent_appearances if i >= 4)  # 3 kì cuối
                momentum = (recent_count / number_frequency[num]) * 100

            trend_stats[num] = {
                'first_part_rate': first_rate,
                'second_part_rate': second_rate,
                'trend': trend,
                'momentum': momentum,
                'total_count': number_frequency[num],
                'total_rate': (number_frequency[num] / total_draws) * 100,
                'last_appearance': 7 - max(recent_appearances) - 1 if recent_appearances else 7
            }

        # Phân loại xu hướng ngắn hạn
        hot_recent = []      # Nóng gần đây
        cold_recent = []     # Lạnh gần đây
        stable_recent = []   # Ổn định

        for num in range(1, 81):
            stats = trend_stats[num]
            # Với 7 kì, ngưỡng nhạy hơn
            if stats['momentum'] > 60 or stats['total_rate'] > 35:  # Nóng
                hot_recent.append(num)
            elif stats['momentum'] < 20 or stats['total_rate'] < 15:  # Lạnh
                cold_recent.append(num)
            else:
                stable_recent.append(num)

        print_info(f"📊 Phân tích xu hướng ngắn hạn {total_draws} kì:")
        print_info(f"� Nóng gần đây: {len(hot_recent)} số")
        print_info(f"❄️ Lạnh gần đây: {len(cold_recent)} số")
        print_info(f"➡️ Ổn định: {len(stable_recent)} số")

        # Top xu hướng ngắn hạn
        sorted_by_momentum = sorted(trend_stats.items(),
                                   key=lambda x: x[1]['momentum'], reverse=True)

        print_info("\n� TOP 10 SỐ NÓNG NGẮN HẠN:")
        for i, (num, stats) in enumerate(sorted_by_momentum[:10]):
            print(f"   {i+1:2d}. Số {num:2d}: {stats['total_count']} lần, momentum {stats['momentum']:.1f}%")

        print_info("\n❄️ TOP 10 SỐ LẠNH NGẮN HẠN:")
        for i, (num, stats) in enumerate(sorted_by_momentum[-10:]):
            print(f"   {i+1:2d}. Số {num:2d}: {stats['total_count']} lần, momentum {stats['momentum']:.1f}%")

        return {
            'trend_stats': trend_stats,
            'hot_recent': hot_recent,
            'cold_recent': cold_recent,
            'stable_recent': stable_recent,
            'period': self.short_term_period
        }

    def create_feature_vectors(self):
        """Tạo vector đặc trưng cho 70 kì và 7 kì"""
        print_header("TẠO VECTOR ĐẶC TRƯNG")

        feature_vectors = {}

        for period in self.feature_periods:
            print_info(f"🔍 Tạo vector đặc trưng cho {period} kì gần nhất")

            if len(self.data) < period:
                print_warning(f"Không đủ dữ liệu cho {period} kì")
                continue

            # Lấy dữ liệu period kì gần nhất
            recent_data = self.data.tail(period).copy()

            # 1. Vector tần suất xuất hiện
            frequency_vector = np.zeros(80)
            for _, row in recent_data.iterrows():
                for num in row['results']:
                    frequency_vector[num-1] += 1

            # Chuẩn hóa theo tỷ lệ phần trăm
            frequency_vector = (frequency_vector / len(recent_data)) * 100

            # 2. Vector khoảng cách lần xuất hiện cuối
            last_appearance = np.full(80, period)  # Mặc định = period (chưa xuất hiện)
            for i, (_, row) in enumerate(recent_data.iterrows()):
                for num in row['results']:
                    last_appearance[num-1] = period - i - 1  # Khoảng cách từ cuối

            # 3. Vector độ ổn định (standard deviation)
            # Chia period thành các window nhỏ để tính std
            window_size = max(1, period // 5)  # Chia thành 5 window
            stability_vector = np.zeros(80)

            for num in range(1, 81):
                window_counts = []
                for w in range(0, period, window_size):
                    window_data = recent_data.iloc[w:w+window_size]
                    count = sum(1 for _, row in window_data.iterrows()
                              if num in row['results'])
                    window_counts.append(count)

                if len(window_counts) > 1:
                    stability_vector[num-1] = np.std(window_counts)

            # 4. Vector xu hướng (slope)
            trend_vector = np.zeros(80)
            if period >= 10:  # Cần ít nhất 10 kì để tính trend
                for num in range(1, 81):
                    appearances = []
                    for i, (_, row) in enumerate(recent_data.iterrows()):
                        count = 1 if num in row['results'] else 0
                        appearances.append(count)

                    # Tính slope bằng linear regression đơn giản
                    x = np.arange(len(appearances))
                    if len(appearances) > 1:
                        slope = np.polyfit(x, appearances, 1)[0]
                        trend_vector[num-1] = slope * 1000  # Scale up

            # 5. Vector pattern (chu kỳ xuất hiện)
            pattern_vector = np.zeros(80)
            for num in range(1, 81):
                appearances = []
                for i, (_, row) in enumerate(recent_data.iterrows()):
                    if num in row['results']:
                        appearances.append(i)

                if len(appearances) >= 2:
                    # Tính khoảng cách trung bình giữa các lần xuất hiện
                    intervals = [appearances[i+1] - appearances[i]
                               for i in range(len(appearances)-1)]
                    pattern_vector[num-1] = np.mean(intervals) if intervals else 0

            # Kết hợp tất cả vectors
            combined_vector = np.concatenate([
                frequency_vector,      # 80 features
                last_appearance,       # 80 features
                stability_vector,      # 80 features
                trend_vector,          # 80 features
                pattern_vector         # 80 features
            ])  # Total: 400 features

            feature_vectors[f'{period}_ki'] = {
                'frequency': frequency_vector,
                'last_appearance': last_appearance,
                'stability': stability_vector,
                'trend': trend_vector,
                'pattern': pattern_vector,
                'combined': combined_vector,
                'period': period
            }

            print_success(f"✅ Vector {period} kì: {len(combined_vector)} features")

        return feature_vectors

    def analyze_missing_patterns(self):
        """Phân tích pattern của số trượt"""
        print_header("PHÂN TÍCH PATTERN SỐ TRƯỢT")

        if len(self.data) < 50:
            print_warning("Không đủ dữ liệu để phân tích pattern")
            return None

        # Phân tích 50 kì gần nhất
        recent_data = self.data.tail(50).copy()

        # Tìm số trượt trong mỗi kì
        missing_patterns = []
        all_numbers = set(range(1, 81))

        for _, row in recent_data.iterrows():
            appeared_numbers = set(row['results'])
            missing_numbers = all_numbers - appeared_numbers
            missing_patterns.append(list(missing_numbers))

        # Thống kê số trượt
        missing_frequency = defaultdict(int)
        for missing_list in missing_patterns:
            for num in missing_list:
                missing_frequency[num] += 1

        # Tính tỷ lệ trượt
        missing_stats = {}
        total_draws = len(missing_patterns)

        for num in range(1, 81):
            miss_count = missing_frequency[num]
            miss_rate = (miss_count / total_draws) * 100
            missing_stats[num] = {
                'miss_count': miss_count,
                'miss_rate': miss_rate,
                'appear_count': total_draws - miss_count,
                'appear_rate': 100 - miss_rate
            }

        # Sắp xếp theo tỷ lệ trượt
        sorted_by_miss = sorted(missing_stats.items(),
                               key=lambda x: x[1]['miss_rate'], reverse=True)

        print_info(f"📊 Phân tích pattern trượt trong {total_draws} kì:")
        print_info("\n🎯 TOP 15 SỐ TRƯỢT NHIỀU NHẤT:")
        for i, (num, stats) in enumerate(sorted_by_miss[:15]):
            print(f"   {i+1:2d}. Số {num:2d}: {stats['miss_count']:2d}/{total_draws} kì ({stats['miss_rate']:.1f}%)")

        return {
            'missing_stats': missing_stats,
            'missing_patterns': missing_patterns,
            'total_draws': total_draws
        }

    def predict_missing_numbers(self, long_trend=None, short_trend=None,
                               feature_vectors=None, missing_patterns=None):
        """Dự đoán 6 số trượt dựa trên phân tích xu hướng"""
        print_header("DỰ ĐOÁN 6 SỐ TRƯỢT")

        # Tính điểm cho từng số
        number_scores = defaultdict(float)

        # 1. Điểm từ xu hướng dài hạn (40% trọng số)
        if long_trend:
            print_info("🔍 Tính điểm từ xu hướng dài hạn...")
            for num in range(1, 81):
                stats = long_trend['frequency_stats'][num]
                # Số lạnh có điểm cao hơn (dễ trượt)
                if stats['percentage'] < 20:  # Rất lạnh
                    number_scores[num] += 40
                elif stats['percentage'] < 25:  # Lạnh
                    number_scores[num] += 25
                elif stats['percentage'] > 30:  # Nóng (ít trượt)
                    number_scores[num] -= 15

        # 2. Điểm từ xu hướng ngắn hạn (30% trọng số)
        if short_trend:
            print_info("🔍 Tính điểm từ xu hướng ngắn hạn...")
            for num in range(1, 81):
                stats = short_trend['trend_stats'][num]
                # Số có xu hướng giảm có điểm cao hơn
                if stats['trend'] < -10:  # Giảm mạnh
                    number_scores[num] += 30
                elif stats['trend'] < -5:  # Giảm vừa
                    number_scores[num] += 20
                elif stats['trend'] > 10:  # Tăng mạnh (ít trượt)
                    number_scores[num] -= 10

        # 3. Điểm từ vector đặc trưng (20% trọng số)
        if feature_vectors:
            print_info("🔍 Tính điểm từ vector đặc trưng...")

            # Sử dụng vector 7 kì nếu có
            if '7_ki' in feature_vectors:
                vector_7 = feature_vectors['7_ki']

                # Điểm từ last_appearance
                for num in range(1, 81):
                    last_app = vector_7['last_appearance'][num-1]
                    if last_app >= 5:  # Không xuất hiện trong 5 kì gần nhất
                        number_scores[num] += 20
                    elif last_app >= 3:
                        number_scores[num] += 10

                # Điểm từ stability (số ít ổn định dễ trượt)
                stability = vector_7['stability']
                stability_mean = np.mean(stability)
                for num in range(1, 81):
                    if stability[num-1] < stability_mean * 0.5:
                        number_scores[num] += 15

        # 4. Điểm từ pattern trượt (10% trọng số)
        if missing_patterns:
            print_info("🔍 Tính điểm từ pattern trượt...")
            for num in range(1, 81):
                stats = missing_patterns['missing_stats'][num]
                # Số trượt nhiều có điểm cao hơn
                if stats['miss_rate'] > 80:  # Trượt > 80%
                    number_scores[num] += 15
                elif stats['miss_rate'] > 70:  # Trượt > 70%
                    number_scores[num] += 10

        # Sắp xếp theo điểm số
        sorted_scores = sorted(number_scores.items(),
                              key=lambda x: x[1], reverse=True)

        # Lấy top 6 số có điểm cao nhất
        predicted_missing = [num for num, score in sorted_scores[:self.missing_count]]
        predicted_missing.sort()

        print_success("✅ Dự đoán hoàn thành!")
        print_info(f"🎯 6 SỐ DỰ ĐOÁN TRƯỢT: {predicted_missing}")

        # Hiển thị chi tiết điểm số
        print_info("\n📊 CHI TIẾT ĐIỂM SỐ TOP 10:")
        for i, (num, score) in enumerate(sorted_scores[:10]):
            print(f"   {i+1:2d}. Số {num:2d}: {score:.1f} điểm")

        return {
            'predicted_missing': predicted_missing,
            'all_scores': dict(sorted_scores),
            'method': 'trend_analysis'
        }

    def run_complete_analysis(self):
        """Chạy phân tích hoàn chỉnh"""
        print_header("PHÂN TÍCH XU HƯỚNG HOÀN CHỈNH")

        # 1. Tải dữ liệu
        if not self.load_data_3_months():
            print_error("Không thể tải dữ liệu")
            return None

        results = {}

        # 2. Phân tích xu hướng dài hạn
        print_info("🔍 Bước 1: Phân tích xu hướng dài hạn...")
        long_trend = self.analyze_long_term_trend()
        results['long_term_trend'] = long_trend

        # 3. Phân tích xu hướng ngắn hạn
        print_info("🔍 Bước 2: Phân tích xu hướng ngắn hạn...")
        short_trend = self.analyze_short_term_trend()
        results['short_term_trend'] = short_trend

        # 4. Tạo vector đặc trưng
        print_info("🔍 Bước 3: Tạo vector đặc trưng...")
        feature_vectors = self.create_feature_vectors()
        results['feature_vectors'] = feature_vectors

        # 5. Phân tích pattern trượt
        print_info("🔍 Bước 4: Phân tích pattern trượt...")
        missing_patterns = self.analyze_missing_patterns()
        results['missing_patterns'] = missing_patterns

        # 6. Dự đoán số trượt
        print_info("🔍 Bước 5: Dự đoán số trượt...")
        prediction = self.predict_missing_numbers(
            long_trend=long_trend,
            short_trend=short_trend,
            feature_vectors=feature_vectors,
            missing_patterns=missing_patterns
        )
        results['prediction'] = prediction

        # Tóm tắt kết quả
        print_header("TÓM TẮT KẾT QUẢ")

        if prediction:
            print_success(f"🎯 DỰ ĐOÁN 6 SỐ TRƯỢT: {prediction['predicted_missing']}")

        if long_trend:
            print_info(f"📊 Xu hướng dài hạn ({self.long_term_period} kì):")
            print_info(f"   • Số nóng: {len(long_trend['hot_numbers'])} số")
            print_info(f"   • Số lạnh: {len(long_trend['cold_numbers'])} số")

        if short_trend:
            print_info(f"📊 Xu hướng ngắn hạn ({self.short_term_period} kì):")
            print_info(f"   • Xu hướng tăng: {len(short_trend['trending_up'])} số")
            print_info(f"   • Xu hướng giảm: {len(short_trend['trending_down'])} số")

        if feature_vectors:
            print_info(f"📊 Vector đặc trưng:")
            for key, vector in feature_vectors.items():
                print_info(f"   • {key}: {len(vector['combined'])} features")

        return results

    def save_analysis_report(self, results, filename=None):
        """Lưu báo cáo phân tích"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"trend_analysis_report_{timestamp}.txt"

        print_info(f"💾 Lưu báo cáo: {filename}")

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("BÁO CÁO PHÂN TÍCH XU HƯỚNG KENO\n")
            f.write("=" * 60 + "\n")
            f.write(f"Thời gian: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Dự đoán chính
            if 'prediction' in results and results['prediction']:
                pred = results['prediction']['predicted_missing']
                f.write(f"🎯 DỰ ĐOÁN 6 SỐ TRƯỢT: {pred}\n\n")

            # Xu hướng dài hạn
            if 'long_term_trend' in results and results['long_term_trend']:
                lt = results['long_term_trend']
                f.write(f"📊 XU HƯỚNG DÀI HẠN ({lt['period']} kì):\n")
                f.write(f"   • Số nóng (>27%): {len(lt['hot_numbers'])} số\n")
                f.write(f"   • Số lạnh (<23%): {len(lt['cold_numbers'])} số\n")
                f.write(f"   • Số ổn định: {len(lt['stable_numbers'])} số\n\n")

            # Xu hướng ngắn hạn
            if 'short_term_trend' in results and results['short_term_trend']:
                st = results['short_term_trend']
                f.write(f"📊 XU HƯỚNG NGẮN HẠN ({st['period']} kì):\n")
                f.write(f"   • Xu hướng tăng: {len(st['trending_up'])} số\n")
                f.write(f"   • Xu hướng giảm: {len(st['trending_down'])} số\n")
                f.write(f"   • Ổn định: {len(st['stable_trend'])} số\n\n")

            # Chi tiết điểm số
            if 'prediction' in results and results['prediction']:
                f.write("📊 CHI TIẾT ĐIỂM SỐ TOP 20:\n")
                scores = results['prediction']['all_scores']
                sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
                for i, (num, score) in enumerate(sorted_scores[:20]):
                    f.write(f"   {i+1:2d}. Số {num:2d}: {score:.1f} điểm\n")

        print_success(f"✅ Đã lưu báo cáo: {filename}")

def demo_trend_analysis():
    """Demo phân tích xu hướng"""
    print_header("DEMO PHÂN TÍCH XU HƯỚNG KENO")

    # Tạo analyzer
    analyzer = TrendAnalyzer()

    # Chạy phân tích hoàn chỉnh
    results = analyzer.run_complete_analysis()

    if results:
        # Lưu báo cáo
        analyzer.save_analysis_report(results)

        print_header("DEMO HOÀN THÀNH")
        print_success("✅ Phân tích xu hướng thành công!")

        if 'prediction' in results and results['prediction']:
            predicted = results['prediction']['predicted_missing']
            print_info(f"🎯 Kết quả dự đoán: {predicted}")
            print_info("💡 Sử dụng kết quả này để tham khảo dự đoán số trượt")
    else:
        print_error("❌ Phân tích thất bại")

def quick_prediction():
    """Dự đoán nhanh 6 số trượt"""
    print_header("DỰ ĐOÁN NHANH 6 SỐ TRƯỢT")

    analyzer = TrendAnalyzer()

    # Chỉ tải dữ liệu và dự đoán
    if analyzer.load_data_3_months():
        # Phân tích nhanh
        long_trend = analyzer.analyze_long_term_trend()
        short_trend = analyzer.analyze_short_term_trend()

        # Dự đoán
        prediction = analyzer.predict_missing_numbers(
            long_trend=long_trend,
            short_trend=short_trend
        )

        if prediction:
            print_header("KẾT QUẢ DỰ ĐOÁN NHANH")
            print_success(f"🎯 6 SỐ DỰ ĐOÁN TRƯỢT: {prediction['predicted_missing']}")
            return prediction['predicted_missing']

    print_error("❌ Không thể dự đoán")
    return None

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            quick_prediction()
        elif sys.argv[1] == "demo":
            demo_trend_analysis()
        else:
            print_info("Sử dụng: python trend_analysis.py [quick|demo]")
            print_info("  quick: Dự đoán nhanh")
            print_info("  demo:  Demo đầy đủ")
    else:
        demo_trend_analysis()
