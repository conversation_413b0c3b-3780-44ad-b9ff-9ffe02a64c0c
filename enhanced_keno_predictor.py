#!/usr/bin/env python3
"""
Enhanced Keno Predictor - <PERSON><PERSON>ch hợp phân tích xu hướng vào model hiện tại
<PERSON>ết hợp LSTM + Trend Analysis + Pattern Recognition để dự đoán hiệu quả hơn
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from datetime import datetime, timedelta
from collections import defaultdict
from variable_length_model import VariableLengthKenoModel, connect_db, print_header, print_success, print_info, print_warning
from trend_analysis import TrendAnalyzer

class EnhancedKenoPredictor:
    """
    Enhanced Predictor kết hợp:
    1. LSTM Model (từ VariableLengthKenoModel)
    2. Trend Analysis (từ TrendAnalyzer)
    3. Pattern Recognition
    4. Ensemble Prediction với trọng số thông minh
    """

    def __init__(self, model_path='keno_variable_length_model.h5'):
        self.model_path = model_path

        # Core components
        self.lstm_model = None
        self.trend_analyzer = None

        # Prediction weights (có thể điều chỉnh)
        self.weights = {
            'lstm': 0.4,           # LSTM prediction
            'trend_long': 0.25,    # Long-term trend (70 kì)
            'trend_short': 0.2,    # Short-term trend (30 kì)
            'pattern': 0.15        # Pattern analysis
        }

        # Cache để tối ưu performance
        self.trend_cache = {}
        self.cache_timeout = 36000  # 1 hour

        # Historical Analysis & Memory System
        self.historical_memory = {}  # Ghi nhớ số hay ra quá
        self.memory_config = {
            'over_prediction_threshold': 3,  # Nếu dự đoán sai quá 3 lần
            'cooldown_periods': 10,          # Không dự đoán lại trong 10 kì
            'analysis_window': 70,           # Phân tích 70 kì gần nhất
            'frequency_threshold': 0.7       # Ngưỡng tần suất cao (70%)
        }

        self.initialize_components()

    def initialize_components(self):
        """Khởi tạo các thành phần"""
        print_header("KHỞI TẠO ENHANCED PREDICTOR")

        # 1. Load LSTM Model
        try:
            self.lstm_model = VariableLengthKenoModel()
            self.lstm_model.model = tf.keras.models.load_model(self.model_path)
            print_success("✅ Đã load LSTM model")
        except Exception as e:
            print_warning(f"⚠️ Không thể load LSTM model: {e}")

        # 2. Initialize Trend Analyzer
        try:
            self.trend_analyzer = TrendAnalyzer()
            print_success("✅ Đã khởi tạo Trend Analyzer")
        except Exception as e:
            print_warning(f"⚠️ Không thể khởi tạo Trend Analyzer: {e}")

    def get_trend_analysis(self, force_refresh=False):
        """Lấy phân tích xu hướng (có cache)"""
        current_time = datetime.now().timestamp()

        # Kiểm tra cache
        if not force_refresh and 'data' in self.trend_cache:
            cache_time = self.trend_cache.get('timestamp', 0)
            if current_time - cache_time < self.cache_timeout:
                print_info("📋 Sử dụng trend analysis từ cache")
                return self.trend_cache['data']

        print_info("🔄 Thực hiện phân tích xu hướng mới...")

        if not self.trend_analyzer:
            return None

        # Load data và phân tích
        if not self.trend_analyzer.load_data_3_months():
            return None

        # Phân tích các thành phần
        long_trend = self.trend_analyzer.analyze_long_term_trend()
        short_trend = self.trend_analyzer.analyze_short_term_trend()
        feature_vectors = self.trend_analyzer.create_feature_vectors()
        missing_patterns = self.trend_analyzer.analyze_missing_patterns()

        trend_data = {
            'long_trend': long_trend,
            'short_trend': short_trend,
            'feature_vectors': feature_vectors,
            'missing_patterns': missing_patterns
        }

        # Cache kết quả
        self.trend_cache = {
            'data': trend_data,
            'timestamp': current_time
        }

        print_success("✅ Hoàn thành phân tích xu hướng")
        return trend_data

    def analyze_historical_patterns(self, days_back=30):
        """Phân tích pattern lịch sử để tìm số hay ra quá"""
        print_info("🔍 Phân tích pattern lịch sử...")

        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            # Lấy dữ liệu lịch sử
            query = """
                SELECT date, time, results
                FROM histories_keno
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (days_back, self.memory_config['analysis_window']))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < 20:
                print_warning("Không đủ dữ liệu lịch sử")
                return {}

            # Phân tích tần suất xuất hiện
            number_frequency = defaultdict(int)
            total_draws = len(rows)

            for row in rows:
                numbers = [int(n) for n in row['results'].split(',')]
                for num in numbers:
                    number_frequency[num] += 1

            # Tìm số hay ra quá (tần suất > threshold)
            over_frequent_numbers = {}
            threshold = self.memory_config['frequency_threshold']

            for num in range(1, 81):
                frequency = number_frequency[num] / total_draws
                if frequency > threshold:
                    over_frequent_numbers[num] = {
                        'frequency': frequency,
                        'count': number_frequency[num],
                        'total_draws': total_draws,
                        'over_threshold': frequency - threshold
                    }

            print_info(f"📊 Tìm thấy {len(over_frequent_numbers)} số hay ra quá ngưỡng {threshold:.1%}")

            return over_frequent_numbers

        except Exception as e:
            print_warning(f"Lỗi phân tích lịch sử: {e}")
            return {}

    def update_prediction_memory(self, predicted_numbers, actual_results=None):
        """Cập nhật bộ nhớ dự đoán"""
        current_time = datetime.now().timestamp()

        # Cập nhật lịch sử dự đoán
        for num in predicted_numbers:
            if num not in self.historical_memory:
                self.historical_memory[num] = {
                    'prediction_count': 0,
                    'correct_count': 0,
                    'wrong_count': 0,
                    'last_predicted': None,
                    'cooldown_until': None,
                    'over_prediction_streak': 0
                }

            memory = self.historical_memory[num]
            memory['prediction_count'] += 1
            memory['last_predicted'] = current_time

            # Nếu có kết quả thực tế, cập nhật độ chính xác
            if actual_results is not None:
                if num not in actual_results:  # Dự đoán đúng (số trượt)
                    memory['correct_count'] += 1
                    memory['over_prediction_streak'] = 0  # Reset streak
                else:  # Dự đoán sai (số ra)
                    memory['wrong_count'] += 1
                    memory['over_prediction_streak'] += 1

                    # Nếu dự đoán sai quá nhiều lần, đưa vào cooldown
                    if memory['over_prediction_streak'] >= self.memory_config['over_prediction_threshold']:
                        cooldown_duration = self.memory_config['cooldown_periods'] * 8 * 60  # 8 phút/kì
                        memory['cooldown_until'] = current_time + cooldown_duration
                        print_info(f"🚫 Số {num} vào cooldown {self.memory_config['cooldown_periods']} kì")

    def get_blacklisted_numbers(self):
        """Lấy danh sách số đang trong cooldown"""
        current_time = datetime.now().timestamp()
        blacklisted = []

        for num, memory in self.historical_memory.items():
            if memory.get('cooldown_until') and memory['cooldown_until'] > current_time:
                remaining_time = memory['cooldown_until'] - current_time
                remaining_periods = int(remaining_time / (8 * 60))  # 8 phút/kì
                blacklisted.append({
                    'number': num,
                    'remaining_periods': remaining_periods,
                    'reason': f"Dự đoán sai {memory['over_prediction_streak']} lần liên tiếp"
                })

        return blacklisted

    def filter_predictions_by_memory(self, predictions, method_name=""):
        """Lọc dự đoán dựa trên bộ nhớ lịch sử"""
        if not predictions:
            return predictions

        blacklisted = self.get_blacklisted_numbers()
        blacklisted_nums = [item['number'] for item in blacklisted]

        # Lọc bỏ số đang trong cooldown
        filtered = [num for num in predictions if num not in blacklisted_nums]

        if len(filtered) < len(predictions):
            removed = [num for num in predictions if num in blacklisted_nums]
            print_info(f"🚫 {method_name} - Loại bỏ {len(removed)} số trong cooldown: {removed}")

        return filtered

    def get_memory_statistics(self):
        """Thống kê bộ nhớ dự đoán"""
        if not self.historical_memory:
            return None

        stats = {
            'total_numbers_tracked': len(self.historical_memory),
            'numbers_in_cooldown': len(self.get_blacklisted_numbers()),
            'accuracy_stats': {}
        }

        # Tính độ chính xác cho từng số
        for num, memory in self.historical_memory.items():
            total_predictions = memory['prediction_count']
            if total_predictions > 0:
                accuracy = memory['correct_count'] / total_predictions
                stats['accuracy_stats'][num] = {
                    'accuracy': accuracy,
                    'total_predictions': total_predictions,
                    'correct': memory['correct_count'],
                    'wrong': memory['wrong_count'],
                    'streak': memory['over_prediction_streak']
                }

        return stats

    def predict_lstm_only(self, day_results, num_miss=6):
        """Dự đoán chỉ bằng LSTM"""
        if not self.lstm_model or not self.lstm_model.model:
            return []

        try:
            probabilities = self.lstm_model.predict_next_draw(day_results)
            if probabilities is None:
                return []

            # Lấy số có xác suất thấp nhất (dễ trượt)
            prob_list = [(i + 1, prob) for i, prob in enumerate(probabilities)]
            prob_list.sort(key=lambda x: x[1])  # Sắp xếp tăng dần

            return [num for num, _ in prob_list[:num_miss]]
        except Exception as e:
            print_warning(f"Lỗi LSTM prediction: {e}")
            return []

    def predict_trend_based(self, trend_data, num_miss=6):
        """Dự đoán dựa trên trend analysis"""
        if not trend_data:
            return []

        try:
            prediction = self.trend_analyzer.predict_missing_numbers(
                long_trend=trend_data.get('long_trend'),
                short_trend=trend_data.get('short_trend'),
                feature_vectors=trend_data.get('feature_vectors'),
                missing_patterns=trend_data.get('missing_patterns')
            )

            if prediction and 'predicted_missing' in prediction:
                return prediction['predicted_missing'][:num_miss]
            return []
        except Exception as e:
            print_warning(f"Lỗi trend prediction: {e}")
            return []

    def predict_pattern_based(self, day_results, num_miss=6):
        """Dự đoán dựa trên pattern trong ngày hiện tại"""
        if len(day_results) < 10:
            return []

        try:
            # Phân tích pattern trong ngày
            all_numbers = set(range(1, 81))
            appeared_numbers = set()

            for draw in day_results:
                appeared_numbers.update(draw)

            missing_numbers = all_numbers - appeared_numbers

            # Tính tần suất xuất hiện trong ngày
            number_frequency = defaultdict(int)
            for draw in day_results:
                for num in draw:
                    number_frequency[num] += 1

            # Ưu tiên số chưa xuất hiện hoặc xuất hiện ít
            pattern_scores = []
            for num in range(1, 81):
                freq = number_frequency[num]

                if num in missing_numbers:
                    score = 100  # Số chưa xuất hiện có điểm cao nhất
                else:
                    # Số xuất hiện ít có điểm cao hơn
                    score = max(0, 50 - freq * 5)

                pattern_scores.append((num, score))

            # Sắp xếp theo điểm
            pattern_scores.sort(key=lambda x: x[1], reverse=True)
            return [num for num, _ in pattern_scores[:num_miss]]

        except Exception as e:
            print_warning(f"Lỗi pattern prediction: {e}")
            return []

    def ensemble_prediction(self, day_results, num_miss=6, show_details=True):
        """
        Dự đoán ensemble kết hợp tất cả phương pháp + Historical Memory

        Returns:
        dict: Kết quả từ tất cả phương pháp và ensemble final
        """
        if show_details:
            print_header("ENHANCED ENSEMBLE PREDICTION WITH MEMORY")
            print_info(f"🎯 Dự đoán {num_miss} số trượt từ {len(day_results)} kỳ")

        results = {}

        # 0. Kiểm tra blacklist trước
        blacklisted = self.get_blacklisted_numbers()
        if blacklisted and show_details:
            print_info(f"🚫 Có {len(blacklisted)} số trong cooldown:")
            for item in blacklisted[:5]:  # Hiển thị tối đa 5 số
                print_info(f"   • Số {item['number']}: còn {item['remaining_periods']} kì")

        # 1. LSTM Prediction
        if show_details:
            print_info("1️⃣ LSTM Prediction...")
        lstm_pred_raw = self.predict_lstm_only(day_results, num_miss * 2)  # Lấy nhiều hơn để filter
        lstm_pred = self.filter_predictions_by_memory(lstm_pred_raw, "LSTM")[:num_miss]
        results['lstm'] = lstm_pred
        results['lstm_raw'] = lstm_pred_raw

        # 2. Trend Analysis Prediction
        if show_details:
            print_info("2️⃣ Trend Analysis...")
        trend_data = self.get_trend_analysis()
        trend_pred_raw = self.predict_trend_based(trend_data, num_miss * 2)
        trend_pred = self.filter_predictions_by_memory(trend_pred_raw, "Trend")[:num_miss]
        results['trend'] = trend_pred
        results['trend_raw'] = trend_pred_raw

        # 3. Pattern Prediction
        if show_details:
            print_info("3️⃣ Pattern Analysis...")
        pattern_pred_raw = self.predict_pattern_based(day_results, num_miss * 2)
        pattern_pred = self.filter_predictions_by_memory(pattern_pred_raw, "Pattern")[:num_miss]
        results['pattern'] = pattern_pred
        results['pattern_raw'] = pattern_pred_raw

        # 4. Historical Analysis
        if show_details:
            print_info("4️⃣ Historical Analysis...")
        historical_pred = self.predict_historical_based(num_miss)
        results['historical'] = historical_pred

        # 5. Ensemble với trọng số + Memory
        if show_details:
            print_info("5️⃣ Ensemble Combination with Memory...")
        ensemble_pred = self.combine_predictions_with_memory(results, num_miss)
        results['ensemble'] = ensemble_pred

        # 6. Cập nhật memory cho dự đoán này
        self.update_prediction_memory(ensemble_pred)

        if show_details:
            print_success("✅ Enhanced prediction với memory hoàn thành!")
            print_info("📊 KẾT QUẢ (sau khi lọc memory):")
            print_info(f"   🧠 LSTM: {lstm_pred}")
            print_info(f"   📈 Trend: {trend_pred}")
            print_info(f"   🔍 Pattern: {pattern_pred}")
            print_info(f"   📚 Historical: {historical_pred}")
            print_success(f"   🎯 ENSEMBLE: {ensemble_pred}")

            # Hiển thị số bị loại bỏ
            if blacklisted:
                removed_count = sum(len(results[f'{method}_raw']) - len(results[method])
                                  for method in ['lstm', 'trend', 'pattern']
                                  if f'{method}_raw' in results and method in results)
                if removed_count > 0:
                    print_info(f"🚫 Tổng {removed_count} số bị loại bỏ do memory")

        return results

    def predict_historical_based(self, num_miss=6):
        """Dự đoán dựa trên phân tích lịch sử"""
        try:
            # Phân tích số hay ra quá
            over_frequent = self.analyze_historical_patterns()

            if not over_frequent:
                return []

            # Ưu tiên số hay ra quá (có thể sẽ trượt)
            historical_candidates = []
            for num, stats in over_frequent.items():
                # Tính điểm dựa trên mức độ "over-frequent"
                score = stats['over_threshold'] * 100
                historical_candidates.append((num, score))

            # Sắp xếp theo điểm số
            historical_candidates.sort(key=lambda x: x[1], reverse=True)

            # Lấy top numbers và lọc qua memory
            candidates = [num for num, _ in historical_candidates[:num_miss * 2]]
            filtered = self.filter_predictions_by_memory(candidates, "Historical")

            return filtered[:num_miss]

        except Exception as e:
            print_warning(f"Lỗi historical prediction: {e}")
            return []

    def combine_predictions_with_memory(self, predictions, num_miss=6):
        """Kết hợp các dự đoán với trọng số và memory"""
        number_scores = defaultdict(float)

        # Trọng số cho từng phương pháp (bao gồm historical)
        methods = {
            'lstm': self.weights['lstm'] * 0.8,  # Giảm nhẹ để nhường chỗ cho historical
            'trend': self.weights['trend_long'] + self.weights['trend_short'],
            'pattern': self.weights['pattern'],
            'historical': 0.1  # Thêm trọng số cho historical analysis
        }

        # Chuẩn hóa trọng số
        total_weight = sum(methods.values())
        for key in methods:
            methods[key] /= total_weight

        # Tính điểm cho từng số từ các phương pháp
        for method, weight in methods.items():
            if method in predictions and predictions[method]:
                pred_list = predictions[method]
                for i, num in enumerate(pred_list[:num_miss]):
                    # Điểm giảm dần theo thứ tự
                    score = weight * (num_miss - i) / num_miss
                    number_scores[num] += score

        # Bonus điểm cho số có accuracy cao trong memory
        memory_stats = self.get_memory_statistics()
        if memory_stats and memory_stats['accuracy_stats']:
            for num, score in number_scores.items():
                if num in memory_stats['accuracy_stats']:
                    accuracy = memory_stats['accuracy_stats'][num]['accuracy']
                    if accuracy > 0.6:  # Nếu accuracy > 60%
                        number_scores[num] += 0.1 * accuracy  # Bonus điểm

        # Sắp xếp theo điểm số
        sorted_scores = sorted(number_scores.items(),
                              key=lambda x: x[1], reverse=True)

        # Lấy top numbers và đảm bảo không trong blacklist
        ensemble_candidates = [num for num, _ in sorted_scores]
        final_result = self.filter_predictions_by_memory(ensemble_candidates, "Ensemble")

        return final_result[:num_miss]

    def combine_predictions(self, predictions, num_miss=6):
        """Kết hợp các dự đoán với trọng số"""
        number_scores = defaultdict(float)

        # Tính điểm cho từng số từ các phương pháp
        methods = {
            'lstm': self.weights['lstm'],
            'trend': self.weights['trend_long'] + self.weights['trend_short'],
            'pattern': self.weights['pattern']
        }

        for method, weight in methods.items():
            if method in predictions and predictions[method]:
                pred_list = predictions[method]
                for i, num in enumerate(pred_list[:num_miss]):
                    # Điểm giảm dần theo thứ tự
                    score = weight * (num_miss - i) / num_miss
                    number_scores[num] += score

        # Sắp xếp theo điểm số
        sorted_scores = sorted(number_scores.items(),
                              key=lambda x: x[1], reverse=True)

        # Lấy top numbers
        ensemble_result = [num for num, _ in sorted_scores[:num_miss]]
        ensemble_result.sort()

        return ensemble_result

    def predict_from_database(self, target_date=None, show_details=True):
        """Dự đoán từ database với enhanced method"""
        if show_details:
            print_header("ENHANCED PREDICTION FROM DATABASE")

        # Lấy dữ liệu từ database
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            if target_date is None:
                cursor.execute("SELECT MAX(date) as max_date FROM histories_keno")
                result = cursor.fetchone()
                target_date = result['max_date']

            query = """
                SELECT time, results FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """
            cursor.execute(query, (target_date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < 50:
                print_warning(f"Không đủ dữ liệu cho ngày {target_date}")
                return None

            # Chuyển đổi dữ liệu
            day_results = []
            for row in rows:
                numbers = [int(n) for n in row['results'].split(',')]
                day_results.append(numbers)

            if show_details:
                print_info(f"📅 Ngày: {target_date}")
                print_info(f"📊 Số kỳ: {len(day_results)}")

            # Dự đoán
            results = self.ensemble_prediction(day_results, show_details=show_details)

            # Thêm metadata
            results['date'] = target_date
            results['input_draws'] = len(day_results)
            results['target_draw'] = len(day_results) + 1

            return results

        except Exception as e:
            print_warning(f"Lỗi lấy dữ liệu: {e}")
            return None

    def quick_predict(self, num_miss=6):
        """Dự đoán nhanh với cài đặt mặc định"""
        print_header("QUICK ENHANCED PREDICTION")

        result = self.predict_from_database(show_details=False)

        if result and 'ensemble' in result:
            print_success(f"🎯 DỰ ĐOÁN NHANH: {result['ensemble']}")
            return result['ensemble']

        print_warning("❌ Không thể dự đoán")
        return None

    def compare_methods(self, target_date=None):
        """So sánh hiệu quả các phương pháp"""
        print_header("SO SÁNH CÁC PHƯƠNG PHÁP")

        result = self.predict_from_database(target_date, show_details=False)

        if not result:
            return None

        print_info(f"📅 Ngày: {result['date']}")
        print_info(f"📊 Từ {result['input_draws']} kỳ dự đoán kỳ {result['target_draw']}")
        print_info("")

        methods = ['lstm', 'trend', 'pattern', 'ensemble']
        for method in methods:
            if method in result:
                icon = {'lstm': '🧠', 'trend': '📈', 'pattern': '🔍', 'ensemble': '🎯'}
                print_info(f"{icon.get(method, '•')} {method.upper()}: {result[method]}")

        return result

    def adjust_weights(self, lstm=None, trend_long=None, trend_short=None, pattern=None):
        """Điều chỉnh trọng số các phương pháp"""
        print_header("ĐIỀU CHỈNH TRỌNG SỐ")

        if lstm is not None:
            self.weights['lstm'] = lstm
        if trend_long is not None:
            self.weights['trend_long'] = trend_long
        if trend_short is not None:
            self.weights['trend_short'] = trend_short
        if pattern is not None:
            self.weights['pattern'] = pattern

        # Chuẩn hóa trọng số
        total = sum(self.weights.values())
        if total > 0:
            for key in self.weights:
                self.weights[key] /= total

        print_info("📊 Trọng số mới:")
        for method, weight in self.weights.items():
            print_info(f"   • {method}: {weight:.2f}")

    def get_model_info(self):
        """Hiển thị thông tin model"""
        print_header("THÔNG TIN ENHANCED MODEL")

        print_info("🧠 LSTM Model:")
        if self.lstm_model and self.lstm_model.model:
            print_info(f"   ✅ Đã load từ: {self.model_path}")
            print_info(f"   📏 Max sequence: {self.lstm_model.max_sequence_length}")
        else:
            print_info("   ❌ Chưa load")

        print_info("📈 Trend Analyzer:")
        if self.trend_analyzer:
            print_info(f"   ✅ Sẵn sàng")
            print_info(f"   📊 Long-term: {self.trend_analyzer.long_term_period} kỳ")
            print_info(f"   📊 Short-term: {self.trend_analyzer.short_term_period} kỳ")
        else:
            print_info("   ❌ Chưa khởi tạo")

        print_info("⚖️ Trọng số hiện tại:")
        for method, weight in self.weights.items():
            print_info(f"   • {method}: {weight:.2f}")

        print_info("💾 Cache:")
        if 'data' in self.trend_cache:
            cache_age = datetime.now().timestamp() - self.trend_cache.get('timestamp', 0)
            print_info(f"   📋 Trend cache: {cache_age/60:.1f} phút")
        else:
            print_info("   📋 Trend cache: Trống")

        # Thông tin Memory System
        print_info("🧠 Memory System:")
        memory_stats = self.get_memory_statistics()
        if memory_stats:
            print_info(f"   📊 Tracking {memory_stats['total_numbers_tracked']} số")
            print_info(f"   🚫 {memory_stats['numbers_in_cooldown']} số trong cooldown")

            # Top accuracy numbers
            if memory_stats['accuracy_stats']:
                sorted_accuracy = sorted(memory_stats['accuracy_stats'].items(),
                                       key=lambda x: x[1]['accuracy'], reverse=True)
                print_info("   🎯 Top 3 accuracy:")
                for i, (num, stats) in enumerate(sorted_accuracy[:3]):
                    print_info(f"      {i+1}. Số {num}: {stats['accuracy']:.1%} ({stats['correct']}/{stats['total_predictions']})")
        else:
            print_info("   📊 Chưa có dữ liệu memory")

    def clear_cache(self):
        """Xóa cache"""
        self.trend_cache = {}
        print_success("✅ Đã xóa cache")

    def clear_memory(self):
        """Xóa memory system"""
        self.historical_memory = {}
        print_success("✅ Đã xóa memory system")

    def save_memory_to_file(self, filename="prediction_memory.json"):
        """Lưu memory vào file"""
        import json
        try:
            with open(filename, 'w') as f:
                json.dump(self.historical_memory, f, indent=2)
            print_success(f"✅ Đã lưu memory vào {filename}")
        except Exception as e:
            print_warning(f"❌ Lỗi lưu memory: {e}")

    def load_memory_from_file(self, filename="prediction_memory.json"):
        """Load memory từ file"""
        import json
        try:
            with open(filename, 'r') as f:
                self.historical_memory = json.load(f)
            print_success(f"✅ Đã load memory từ {filename}")
        except Exception as e:
            print_warning(f"❌ Lỗi load memory: {e}")

    def configure_memory_system(self, over_prediction_threshold=None,
                               cooldown_periods=None, analysis_window=None,
                               frequency_threshold=None):
        """Cấu hình memory system"""
        print_header("CẤU HÌNH MEMORY SYSTEM")

        if over_prediction_threshold is not None:
            self.memory_config['over_prediction_threshold'] = over_prediction_threshold
        if cooldown_periods is not None:
            self.memory_config['cooldown_periods'] = cooldown_periods
        if analysis_window is not None:
            self.memory_config['analysis_window'] = analysis_window
        if frequency_threshold is not None:
            self.memory_config['frequency_threshold'] = frequency_threshold

        print_info("📊 Cấu hình mới:")
        for key, value in self.memory_config.items():
            print_info(f"   • {key}: {value}")

    def get_memory_report(self):
        """Báo cáo chi tiết memory system"""
        print_header("BÁO CÁO MEMORY SYSTEM")

        if not self.historical_memory:
            print_info("📊 Memory system trống")
            return

        stats = self.get_memory_statistics()
        blacklisted = self.get_blacklisted_numbers()

        print_info(f"📊 Tổng quan:")
        print_info(f"   • Tracking: {stats['total_numbers_tracked']} số")
        print_info(f"   • Cooldown: {len(blacklisted)} số")

        # Top accuracy
        if stats['accuracy_stats']:
            sorted_accuracy = sorted(stats['accuracy_stats'].items(),
                                   key=lambda x: x[1]['accuracy'], reverse=True)
            print_info("\n🎯 TOP 10 ACCURACY:")
            for i, (num, data) in enumerate(sorted_accuracy[:10]):
                print_info(f"   {i+1:2d}. Số {num:2d}: {data['accuracy']:.1%} "
                          f"({data['correct']}/{data['total_predictions']}) "
                          f"streak: {data['streak']}")

        # Blacklisted numbers
        if blacklisted:
            print_info(f"\n🚫 SỐ TRONG COOLDOWN ({len(blacklisted)}):")
            for item in blacklisted:
                print_info(f"   • Số {item['number']}: còn {item['remaining_periods']} kì - {item['reason']}")

        # Cấu hình hiện tại
        print_info("\n⚙️ CẤU HÌNH:")
        for key, value in self.memory_config.items():
            print_info(f"   • {key}: {value}")

    def test_memory_system(self, test_predictions, test_results):
        """Test memory system với dữ liệu mẫu"""
        print_header("TEST MEMORY SYSTEM")

        print_info(f"🧪 Test với {len(test_predictions)} dự đoán")

        for i, (pred, actual) in enumerate(zip(test_predictions, test_results)):
            print_info(f"\nTest {i+1}: Dự đoán {pred}, Thực tế: {actual}")

            # Cập nhật memory
            self.update_prediction_memory(pred, actual)

            # Hiển thị blacklist sau mỗi test
            blacklisted = self.get_blacklisted_numbers()
            if blacklisted:
                print_info(f"   🚫 Blacklist: {[item['number'] for item in blacklisted]}")

        print_success("✅ Test memory system hoàn thành")

def demo_enhanced_predictor():
    """Demo Enhanced Predictor"""
    print_header("DEMO ENHANCED KENO PREDICTOR")

    # Khởi tạo predictor
    predictor = EnhancedKenoPredictor()

    # Hiển thị thông tin
    predictor.get_model_info()

    # Demo dự đoán
    print_info("\n🔮 Demo dự đoán enhanced...")
    result = predictor.predict_from_database()

    if result:
        print_header("KẾT QUẢ DEMO")
        print_success(f"🎯 Dự đoán chính: {result.get('ensemble', [])}")

        # So sánh các phương pháp
        print_info("\n📊 So sánh phương pháp:")
        predictor.compare_methods()

    return predictor

def interactive_enhanced_predictor():
    """Giao diện tương tác cho Enhanced Predictor"""
    print_header("ENHANCED KENO PREDICTOR - INTERACTIVE")

    predictor = EnhancedKenoPredictor()

    while True:
        print_info("\n📋 MENU ENHANCED PREDICTOR:")
        print("1. Dự đoán nhanh")
        print("2. Dự đoán chi tiết")
        print("3. So sánh phương pháp")
        print("4. Điều chỉnh trọng số")
        print("5. Thông tin model")
        print("6. Xóa cache")
        print("7. 🧠 Báo cáo memory system")
        print("8. 🧠 Cấu hình memory")
        print("9. 🧠 Lưu/Load memory")
        print("10. 🧠 Test memory system")
        print("11. Thoát")

        try:
            choice = input("\nNhập lựa chọn (1-11): ").strip()

            if choice == '1':
                predictor.quick_predict()

            elif choice == '2':
                date = input("Nhập ngày (YYYY-MM-DD) hoặc Enter cho ngày mới nhất: ").strip()
                target_date = date if date else None
                predictor.predict_from_database(target_date)

            elif choice == '3':
                date = input("Nhập ngày (YYYY-MM-DD) hoặc Enter cho ngày mới nhất: ").strip()
                target_date = date if date else None
                predictor.compare_methods(target_date)

            elif choice == '4':
                print_info("Nhập trọng số mới (Enter để giữ nguyên):")
                lstm = input(f"LSTM ({predictor.weights['lstm']:.2f}): ").strip()
                trend_long = input(f"Trend Long ({predictor.weights['trend_long']:.2f}): ").strip()
                trend_short = input(f"Trend Short ({predictor.weights['trend_short']:.2f}): ").strip()
                pattern = input(f"Pattern ({predictor.weights['pattern']:.2f}): ").strip()

                predictor.adjust_weights(
                    lstm=float(lstm) if lstm else None,
                    trend_long=float(trend_long) if trend_long else None,
                    trend_short=float(trend_short) if trend_short else None,
                    pattern=float(pattern) if pattern else None
                )

            elif choice == '5':
                predictor.get_model_info()

            elif choice == '6':
                predictor.clear_cache()

            elif choice == '7':
                predictor.get_memory_report()

            elif choice == '8':
                print_info("Cấu hình Memory System (Enter để giữ nguyên):")
                threshold = input(f"Over prediction threshold ({predictor.memory_config['over_prediction_threshold']}): ").strip()
                cooldown = input(f"Cooldown periods ({predictor.memory_config['cooldown_periods']}): ").strip()
                window = input(f"Analysis window ({predictor.memory_config['analysis_window']}): ").strip()
                freq_threshold = input(f"Frequency threshold ({predictor.memory_config['frequency_threshold']}): ").strip()

                predictor.configure_memory_system(
                    over_prediction_threshold=int(threshold) if threshold else None,
                    cooldown_periods=int(cooldown) if cooldown else None,
                    analysis_window=int(window) if window else None,
                    frequency_threshold=float(freq_threshold) if freq_threshold else None
                )

            elif choice == '9':
                print_info("Memory Management:")
                print("  a. Lưu memory")
                print("  b. Load memory")
                print("  c. Xóa memory")
                sub_choice = input("Chọn (a/b/c): ").strip().lower()

                if sub_choice == 'a':
                    filename = input("Tên file (Enter = prediction_memory.json): ").strip()
                    predictor.save_memory_to_file(filename if filename else "prediction_memory.json")
                elif sub_choice == 'b':
                    filename = input("Tên file (Enter = prediction_memory.json): ").strip()
                    predictor.load_memory_from_file(filename if filename else "prediction_memory.json")
                elif sub_choice == 'c':
                    confirm = input("Xác nhận xóa memory? (y/N): ").strip().lower()
                    if confirm == 'y':
                        predictor.clear_memory()

            elif choice == '10':
                print_info("Test Memory System với dữ liệu mẫu")
                # Tạo dữ liệu test mẫu
                test_predictions = [
                    [1, 5, 12, 23, 45, 67],
                    [2, 8, 15, 28, 44, 66],
                    [1, 9, 16, 29, 43, 65]  # Số 1 lặp lại
                ]
                test_results = [
                    [10, 20, 30, 40, 50, 60],  # 1,5,12,23,45,67 đều trượt (đúng)
                    [2, 11, 22, 33, 44, 55],   # 2,44 ra, còn lại trượt (1 sai, 4 đúng)
                    [1, 13, 24, 35, 46, 57]    # 1 ra (sai), còn lại trượt
                ]
                predictor.test_memory_system(test_predictions, test_results)

            elif choice == '11':
                print_info("Thoát Enhanced Predictor")
                break

            else:
                print_warning("Lựa chọn không hợp lệ")

        except KeyboardInterrupt:
            print_info("\nThoát Enhanced Predictor")
            break
        except Exception as e:
            print_warning(f"Lỗi: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "demo":
            demo_enhanced_predictor()
        elif sys.argv[1] == "interactive":
            interactive_enhanced_predictor()
        elif sys.argv[1] == "quick":
            predictor = EnhancedKenoPredictor()
            predictor.quick_predict()
        else:
            print_info("Sử dụng:")
            print_info("  python enhanced_keno_predictor.py demo        # Demo")
            print_info("  python enhanced_keno_predictor.py interactive # Giao diện tương tác")
            print_info("  python enhanced_keno_predictor.py quick       # Dự đoán nhanh")
    else:
        interactive_enhanced_predictor()
