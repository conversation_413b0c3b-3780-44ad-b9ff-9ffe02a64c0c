#!/usr/bin/env python3
"""
Enhanced Trend Predictor - D<PERSON> đoán ngắn hạn (7 kì) và trung hạn (30 kì)
Kết hợp với LSTM model để dự đoán 6 số trượt hiệu quả
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from variable_length_model import VariableLengthKenoModel, connect_db, print_header, print_success, print_info, print_warning

class EnhancedTrendPredictor:
    """
    Enhanced Predictor với:
    - Dự đoán ngắn hạn: 7 kì gần nhất
    - Dự đoán trung hạn: 30 kì gần nhất  
    - Vector đặc trưng cho 7 kì và 30 kì
    - Kết hợp với LSTM model hiện tại
    """
    
    def __init__(self, model_path='keno_variable_length_model.h5'):
        self.model_path = model_path
        self.short_term_period = 7   # Ngắn hạn: 7 kì
        self.medium_term_period = 30 # Trung hạn: 30 kì
        
        # Load LSTM model
        self.lstm_model = None
        self.load_lstm_model()
        
        # Data storage
        self.data = None
        
    def load_lstm_model(self):
        """Load LSTM model hiện có"""
        try:
            import tensorflow as tf
            self.lstm_model = VariableLengthKenoModel()
            self.lstm_model.model = tf.keras.models.load_model(self.model_path)
            print_success("✅ Đã load LSTM model")
        except Exception as e:
            print_warning(f"⚠️ Không thể load LSTM model: {e}")
    
    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        print_info("📥 Loading dữ liệu gần nhất...")
        
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)
            
            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """
            
            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            if len(rows) < self.medium_term_period:
                print_warning(f"Không đủ dữ liệu: {len(rows)} < {self.medium_term_period}")
                return False
                
            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            self.data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                self.data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })
            
            print_success(f"✅ Đã load {len(self.data)} kì")
            return True
            
        except Exception as e:
            print_warning(f"Lỗi load data: {e}")
            return False
    
    def create_frequency_vector(self, data_period):
        """Tạo vector tần suất xuất hiện"""
        frequency_vector = np.zeros(80)
        total_draws = len(data_period)
        
        if total_draws == 0:
            return frequency_vector
            
        for draw in data_period:
            for num in draw['numbers']:
                if 1 <= num <= 80:
                    frequency_vector[num-1] += 1
        
        # Chuyển thành tỷ lệ phần trăm
        frequency_vector = (frequency_vector / total_draws) * 100
        return frequency_vector
    
    def create_missing_vector(self, data_period):
        """Tạo vector số lần bị trượt"""
        missing_vector = np.zeros(80)
        total_draws = len(data_period)
        
        if total_draws == 0:
            return missing_vector
            
        for draw in data_period:
            appeared_numbers = set(draw['numbers'])
            for num in range(1, 81):
                if num not in appeared_numbers:
                    missing_vector[num-1] += 1
        
        # Chuyển thành tỷ lệ phần trăm
        missing_vector = (missing_vector / total_draws) * 100
        return missing_vector
    
    def analyze_short_term_trend(self):
        """Phân tích xu hướng ngắn hạn 7 kì"""
        print_header(f"PHÂN TÍCH XU HƯỚNG NGẮN HẠN ({self.short_term_period} KÌ)")
        
        if len(self.data) < self.short_term_period:
            print_warning(f"Không đủ dữ liệu cho phân tích {self.short_term_period} kì")
            return None
        
        # Lấy 7 kì gần nhất
        recent_data = self.data[-self.short_term_period:]
        
        # Tạo vector đặc trưng
        frequency_vector = self.create_frequency_vector(recent_data)
        missing_vector = self.create_missing_vector(recent_data)
        
        # Phân tích pattern xuất hiện
        number_stats = {}
        for num in range(1, 81):
            appearances = []
            for i, draw in enumerate(recent_data):
                if num in draw['numbers']:
                    appearances.append(i)
            
            # Tính các chỉ số
            freq_rate = frequency_vector[num-1]
            miss_rate = missing_vector[num-1]
            last_appearance = max(appearances) if appearances else -1
            gap_from_last = len(recent_data) - 1 - last_appearance if last_appearance >= 0 else len(recent_data)
            
            number_stats[num] = {
                'frequency_rate': freq_rate,
                'missing_rate': miss_rate,
                'appearances': len(appearances),
                'last_appearance': last_appearance,
                'gap_from_last': gap_from_last,
                'momentum': freq_rate / 7 * 100  # Momentum trong 7 kì
            }
        
        print_info(f"📊 Phân tích {len(recent_data)} kì gần nhất")
        
        return {
            'period': self.short_term_period,
            'frequency_vector': frequency_vector,
            'missing_vector': missing_vector,
            'number_stats': number_stats,
            'data': recent_data
        }
    
    def analyze_medium_term_trend(self):
        """Phân tích xu hướng trung hạn 30 kì"""
        print_header(f"PHÂN TÍCH XU HƯỚNG TRUNG HẠN ({self.medium_term_period} KÌ)")
        
        if len(self.data) < self.medium_term_period:
            print_warning(f"Không đủ dữ liệu cho phân tích {self.medium_term_period} kì")
            return None
        
        # Lấy 30 kì gần nhất
        recent_data = self.data[-self.medium_term_period:]
        
        # Tạo vector đặc trưng
        frequency_vector = self.create_frequency_vector(recent_data)
        missing_vector = self.create_missing_vector(recent_data)
        
        # Phân tích xu hướng tăng/giảm (so sánh 2 nửa)
        half_point = len(recent_data) // 2
        first_half = recent_data[:half_point]
        second_half = recent_data[half_point:]
        
        first_half_freq = self.create_frequency_vector(first_half)
        second_half_freq = self.create_frequency_vector(second_half)
        
        # Tính xu hướng cho từng số
        number_stats = {}
        for num in range(1, 81):
            trend = second_half_freq[num-1] - first_half_freq[num-1]
            
            # Tính độ ổn định (standard deviation)
            appearances_per_window = []
            window_size = 5  # Chia 30 kì thành 6 window
            for i in range(0, len(recent_data), window_size):
                window = recent_data[i:i+window_size]
                count = sum(1 for draw in window if num in draw['numbers'])
                appearances_per_window.append(count)
            
            stability = np.std(appearances_per_window) if len(appearances_per_window) > 1 else 0
            
            number_stats[num] = {
                'frequency_rate': frequency_vector[num-1],
                'missing_rate': missing_vector[num-1],
                'trend': trend,
                'stability': stability,
                'first_half_freq': first_half_freq[num-1],
                'second_half_freq': second_half_freq[num-1]
            }
        
        print_info(f"📊 Phân tích {len(recent_data)} kì gần nhất")
        
        return {
            'period': self.medium_term_period,
            'frequency_vector': frequency_vector,
            'missing_vector': missing_vector,
            'number_stats': number_stats,
            'data': recent_data
        }
