#!/usr/bin/env python3
"""
Test dự đoán real-time với logic:
1. Dự đo<PERSON> kỳ X
2. Loop chờ 1 phút để kiểm tra kỳ X có trong DB chưa
3. So s<PERSON>h kết quả và lưu data + label
4. <PERSON><PERSON><PERSON><PERSON> tục dự đo<PERSON> kỳ X+1
"""

import time
import json
import pandas as pd
from datetime import datetime, timedelta
from variable_length_model import connect_db, print_header, print_success, print_info, print_warning
from predict_keno import KenoPredictor

class RealtimePredictionTest:
    def __init__(self, test_date='2025-05-27'):
        self.test_date = test_date
        self.predictor = KenoPredictor()
        self.min_draws_for_prediction = 50
        self.check_interval = 60  # 1 phút
        self.max_wait_time = 600   # 10 phút tối đa
        self.prediction_results = []
        self.training_data = []

    def get_current_draws_count(self):
        """<PERSON><PERSON><PERSON> số kỳ hiện tại trong database cho ngày test"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            cursor.execute("""
                SELECT COUNT(*) as count
                FROM histories_keno
                WHERE date = %s
            """, (self.test_date,))

            result = cursor.fetchone()
            count = result['count']

            cursor.close()
            conn.close()

            return count

        except Exception as e:
            print_warning(f"❌ Lỗi đếm kỳ: {e}")
            return 0

    def get_day_data_up_to_draw(self, max_draw):
        """Lấy dữ liệu từ kỳ 1 đến kỳ max_draw"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
                LIMIT %s
            """

            cursor.execute(query, (self.test_date, max_draw))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            return rows

        except Exception as e:
            print_warning(f"Lỗi lấy dữ liệu: {e}")
            return None

    def get_specific_draw_result(self, draw_number):
        """Lấy kết quả của kỳ cụ thể (draw_number bắt đầu từ 1)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
                LIMIT %s, 1
            """

            cursor.execute(query, (self.test_date, draw_number - 1))
            row = cursor.fetchone()

            cursor.close()
            conn.close()

            if row:
                row['results'] = [int(n) for n in row['results'].split(',')]
                return row
            return None

        except Exception as e:
            print_warning(f"Lỗi lấy kết quả kỳ {draw_number}: {e}")
            return None

    def predict_next_draw_from_data(self, draws_data, use_hybrid=True):
        """Dự đoán kỳ tiếp theo từ dữ liệu các kỳ trước"""
        if len(draws_data) < self.min_draws_for_prediction:
            return None

        # Chuyển đổi thành format cho model
        day_results = [row['results'] for row in draws_data]

        # Sử dụng predictor để dự đoán với hybrid
        result = self.predictor.predict_next_draw(day_results, show_details=False, use_hybrid=use_hybrid)

        if result:
            prediction_data = {
                'predicted_numbers': result['miss_numbers'],  # Ensemble result
                'input_draws': len(draws_data),
                'target_draw': len(draws_data) + 1,
                'prediction_method': 'hybrid' if use_hybrid else 'lstm_only'
            }

            # Thêm chi tiết hybrid nếu có
            if use_hybrid and 'hybrid_results' in result:
                hybrid = result['hybrid_results']
                prediction_data.update({
                    'lstm_numbers': hybrid.get('lstm', []),
                    'std_numbers': hybrid.get('std_analysis', []),
                    'ensemble_numbers': hybrid.get('ensemble', [])
                })

            return prediction_data
        return None

    def wait_for_draw_result(self, draw_number, max_wait_minutes=10):
        """Chờ kết quả của kỳ draw_number xuất hiện trong DB"""
        print_info(f"⏳ Chờ kết quả kỳ {draw_number} xuất hiện trong DB...")

        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60

        while time.time() - start_time < max_wait_seconds:
            current_count = self.get_current_draws_count()

            if current_count >= draw_number:
                # Kỳ đã có trong DB
                result = self.get_specific_draw_result(draw_number)
                if result:
                    elapsed = time.time() - start_time
                    print_success(f"✅ Kỳ {draw_number} đã có kết quả sau {elapsed:.1f}s")
                    return result

            # Chờ 1 phút trước khi kiểm tra lại
            print_info(f"   📊 Hiện có {current_count} kỳ, cần kỳ {draw_number}. Chờ {self.check_interval}s...")
            time.sleep(self.check_interval)

        print_warning(f"⏰ Timeout: Không có kết quả kỳ {draw_number} sau {max_wait_minutes} phút")
        return None

    def calculate_accuracy(self, predicted, actual):
        """Tính độ chính xác dự đoán số trượt (số KHÔNG xuất hiện)"""
        if not predicted or not actual:
            return 0, []

        # Số dự đoán đúng = số dự đoán KHÔNG có trong kết quả thực tế
        actual_set = set(actual)
        correct_numbers = [num for num in predicted if num not in actual_set]
        accuracy = len(correct_numbers) / len(predicted) * 100

        return accuracy, correct_numbers

    def save_prediction_result(self, prediction_data, actual_result, accuracy, correct_numbers):
        """Lưu kết quả dự đoán để phân tích và tạo training data"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'target_draw': prediction_data['target_draw'],
            'input_draws': prediction_data['input_draws'],
            'predicted_numbers': prediction_data['predicted_numbers'],
            'actual_numbers': actual_result['results'],
            'accuracy': accuracy,
            'correct_numbers': correct_numbers,
            'correct_count': len(correct_numbers),
            'period': actual_result['period'],
            'time': str(actual_result['time']),
            'prediction_method': prediction_data.get('prediction_method', 'unknown')
        }

        # Chỉ sử dụng LSTM, không cần hybrid details

        self.prediction_results.append(result)

        # Tạo training data với labels chi tiết
        input_sequence = [row['results'] for row in self.get_day_data_up_to_draw(prediction_data['input_draws'])]

        training_sample = {
            'timestamp': datetime.now().isoformat(),
            'input_sequence': input_sequence,
            'input_draws_count': prediction_data['input_draws'],
            'target_draw': prediction_data['target_draw'],

            # Predictions
            'predicted_numbers': prediction_data['predicted_numbers'],
            'actual_numbers': actual_result['results'],

            # Labels cho classification
            'is_correct': accuracy > 0,  # Binary: có ít nhất 1 số đúng
            'accuracy_score': accuracy / 100,  # Regression: score từ 0-1
            'correct_count': len(correct_numbers),  # Count: số lượng số đúng

            # Labels chi tiết cho từng số
            'number_labels': self._create_number_labels(prediction_data['predicted_numbers'], actual_result['results']),

            # Metadata
            'prediction_method': prediction_data.get('prediction_method', 'unknown'),
            'period': actual_result['period'],
            'time': str(actual_result['time'])
        }

        # Chỉ sử dụng LSTM prediction, không cần hybrid data

        self.training_data.append(training_sample)

        return result

    def _create_number_labels(self, predicted_numbers, actual_numbers):
        """Tạo labels cho từng số dự đoán số trượt (1: đúng - không xuất hiện, 0: sai - có xuất hiện)"""
        labels = []
        actual_set = set(actual_numbers)

        for num in predicted_numbers:
            # Đúng nếu số dự đoán KHÔNG có trong kết quả thực tế (số trượt)
            labels.append(1 if num not in actual_set else 0)

        return labels

    def run_realtime_prediction_test(self, start_from_draw=51, end_at_draw=119):
        """Chạy test dự đoán real-time"""
        print_header(f"REALTIME PREDICTION TEST - NGÀY {self.test_date}")

        # Kiểm tra model
        if self.predictor.model is None:
            print_warning("Không thể load model. Hãy train model trước:")
            print_info("python train_keno_model.py")
            return

        print_info(f"🎯 Dự đoán từ kỳ {start_from_draw} đến kỳ {end_at_draw}")
        print_info(f"🧠 Sử dụng: LSTM Model đã train")
        print_info(f"⏰ Kiểm tra DB mỗi {self.check_interval} giây")
        print_info(f"⏱️ Timeout tối đa: {self.max_wait_time // 60} phút/kỳ")

        successful_predictions = 0
        total_predictions = 0

        for target_draw in range(start_from_draw, end_at_draw + 1):
            print_header(f"DỰ ĐOÁN KỲ {target_draw}")

            # Bước 1: Lấy dữ liệu để dự đoán
            input_draws_count = target_draw - 1
            input_data = self.get_day_data_up_to_draw(input_draws_count)

            if not input_data or len(input_data) < self.min_draws_for_prediction:
                print_warning(f"⚠️ Không đủ dữ liệu để dự đoán kỳ {target_draw}")
                continue

            print_info(f"📊 Sử dụng {len(input_data)} kỳ trước đó để dự đoán")

            # Bước 2: Thực hiện dự đoán (chỉ sử dụng LSTM model đã train)
            prediction = self.predict_next_draw_from_data(input_data, use_hybrid=False)

            if not prediction:
                print_warning(f"❌ Không thể dự đoán kỳ {target_draw}")
                continue

            print_success(f"🔮 Dự đoán 6 số trượt kỳ {target_draw} (LSTM): {prediction['predicted_numbers']}")

            # Bước 3: Chờ kết quả xuất hiện trong DB
            actual_result = self.wait_for_draw_result(target_draw, self.max_wait_time // 60)

            if not actual_result:
                print_warning(f"⏰ Không có kết quả cho kỳ {target_draw}, bỏ qua")
                continue

            # Bước 4: So sánh kết quả
            print_info(f"📊 20 số thực tế kỳ {target_draw}: {actual_result['results']}")

            accuracy, correct_numbers = self.calculate_accuracy(
                prediction['predicted_numbers'],
                actual_result['results']
            )

            print_info(f"🎯 Độ chính xác LSTM: {accuracy:.1f}% ({len(correct_numbers)}/6 số trượt đúng)")
            if correct_numbers:
                print_success(f"✅ Số trượt dự đoán đúng: {correct_numbers}")

            # Hiển thị số dự đoán sai (có xuất hiện)
            wrong_numbers = [num for num in prediction['predicted_numbers'] if num in actual_result['results']]
            if wrong_numbers:
                print_warning(f"❌ Số dự đoán sai (có xuất hiện): {wrong_numbers}")

            # Bước 5: Lưu kết quả
            self.save_prediction_result(prediction, actual_result, accuracy, correct_numbers)

            if accuracy > 0:
                successful_predictions += 1
            total_predictions += 1

            print_info(f"📈 Tiến độ: {total_predictions}/{end_at_draw - start_from_draw + 1}")
            print_info(f"✅ Thành công: {successful_predictions}/{total_predictions} ({successful_predictions/total_predictions*100:.1f}%)")

            # Nghỉ ngắn trước kỳ tiếp theo
            if target_draw < end_at_draw:
                print_info("⏸️ Nghỉ 5 giây trước kỳ tiếp theo...")
                time.sleep(5)

        # Tổng kết
        self.print_final_summary()
        self.save_results_to_files()

    def print_final_summary(self):
        """In tổng kết cuối cùng"""
        print_header("TỔNG KẾT REALTIME PREDICTION TEST")

        if not self.prediction_results:
            print_warning("❌ Không có kết quả nào")
            return

        total = len(self.prediction_results)
        successful = sum(1 for r in self.prediction_results if r['accuracy'] > 0)
        avg_accuracy = sum(r['accuracy'] for r in self.prediction_results) / total

        print_info(f"📊 Tổng số dự đoán: {total}")
        print_info(f"✅ Dự đoán có ít nhất 1 số trượt đúng: {successful} ({successful/total*100:.1f}%)")
        print_info(f"📈 Độ chính xác trung bình số trượt (LSTM): {avg_accuracy:.2f}%")

        # Thống kê chi tiết
        accuracy_distribution = {}
        for result in self.prediction_results:
            correct_count = result['correct_count']
            if correct_count not in accuracy_distribution:
                accuracy_distribution[correct_count] = 0
            accuracy_distribution[correct_count] += 1

        print_info(f"\n📊 Phân bố số lượng số trượt đúng (LSTM):")
        for correct_count in sorted(accuracy_distribution.keys()):
            count = accuracy_distribution[correct_count]
            percentage = count / total * 100
            print_info(f"   {correct_count} số trượt đúng: {count} lần ({percentage:.1f}%)")

        # Thống kê training data
        print_info(f"\n💾 Training Data Generated:")
        print_info(f"   📊 Total samples: {len(self.training_data)}")
        if self.training_data:
            positive_samples = sum(1 for sample in self.training_data if sample['is_correct'])
            print_info(f"   ✅ Positive samples: {positive_samples} ({positive_samples/len(self.training_data)*100:.1f}%)")
            print_info(f"   ❌ Negative samples: {len(self.training_data)-positive_samples} ({(len(self.training_data)-positive_samples)/len(self.training_data)*100:.1f}%)")

    def save_results_to_files(self):
        """Lưu kết quả vào files"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Lưu prediction results
        results_file = f"prediction_results_{self.test_date}_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.prediction_results, f, indent=2, ensure_ascii=False)
        print_success(f"💾 Đã lưu kết quả dự đoán: {results_file}")

        # Lưu training data
        training_file = f"training_data_{self.test_date}_{timestamp}.json"
        with open(training_file, 'w', encoding='utf-8') as f:
            json.dump(self.training_data, f, indent=2, ensure_ascii=False)
        print_success(f"💾 Đã lưu training data: {training_file}")

        # Tạo CSV summary
        if self.prediction_results:
            df = pd.DataFrame(self.prediction_results)
            csv_file = f"prediction_summary_{self.test_date}_{timestamp}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print_success(f"💾 Đã lưu CSV summary: {csv_file}")

def main():
    """Main function"""
    print_header("REALTIME PREDICTION TEST")

    test = RealtimePredictionTest('2025-05-27')

    print_info("Cấu hình test:")
    print(f"• Ngày test: {test.test_date}")
    print(f"• Model: LSTM đã train (keno_variable_length_model.h5)")
    print(f"• Kiểm tra DB mỗi: {test.check_interval} giây")
    print(f"• Timeout tối đa: {test.max_wait_time // 60} phút/kỳ")
    print(f"• Minimum draws để dự đoán: {test.min_draws_for_prediction}")

    # Nhập range dự đoán
    start_draw = int(input(f"\nNhập kỳ bắt đầu dự đoán (mặc định 51): ") or "51")
    end_draw = int(input(f"Nhập kỳ kết thúc dự đoán (mặc định 60): ") or "60")

    confirm = input(f"\nXác nhận chạy test từ kỳ {start_draw} đến {end_draw}? (y/n): ")
    if confirm.lower() != 'y':
        print_info("❌ Hủy bỏ test")
        return

    # Chạy test
    test.run_realtime_prediction_test(start_draw, end_draw)

if __name__ == "__main__":
    main()
